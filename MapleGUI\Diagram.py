'''Class which contains canvas (self.scene) for inserting nodes and creating pipeline.
Functions->
addObjectModel: assigns model to self.shape
addObject: assigns non-model objects to self.shape
mouseDoubleClickEvent: inserts object in self.shape to diagram at position of double-click
addArrow: inserts arrow from first object selected to second object
getTotalCost: returns total cost of models in diagram
keyPressEvent: deletes selected object from diagram
calcCloudCost: returns total cost of deploying models in diagram to cloud 
updateConfigList: updates list of configurations in each model
updateTfrList: updates list of transformers in each non-model object
removeTfrFromList: removes transformer from list
getNetLatency: returns latency of pipeline
'''

import os
from dotenv import load_dotenv
from collections import deque
import pandas as pd
from globalslist import awslist
from PyQt5.QtWidgets import QApplication, QWidget, QGroupBox, QVBoxLayout, QScrollArea
from PyQt5.QtWidgets import QDialog
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableView, QVBoxLayout, QPushButton, 
    QLineEdit, QComboBox, QWidget, QHBoxLayout
)
from PyQt5.QtGui import QStandardItemModel, QStandardItem
from PyQt5 import QtCore
from PyQt5.QtGui import QPixmap,QColor
from UniqueModel import UniqueModel
from CloudCost import *
from Arrow import Arrow
from Shape import Shape
from StorageShape import StorageShape
from ModelShape import ModelShape
from PyQt5.QtGui import QPainter, QColor, QPen
from PyQt5.QtWidgets import QComboBox,QGraphicsItemGroup,QFileDialog,QLabel,QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsPixmapItem, QGraphicsRectItem, QGraphicsTextItem, QGraphicsView, QGraphicsScene, QMessageBox
from PyQt5.QtCore import QPointF, Qt
from PyQt5.QtWidgets import QFileDialog,QSizePolicy,QCheckBox
from PyQt5.QtWidgets import QGroupBox,QFormLayout,QLabel,QLineEdit
from PyQt5.QtCore import QPointF, QSize, Qt
from PyQt5 import QtGui
import CSWidgets
from PyQt5.QtWidgets import QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
#from PyQt5.QtChart import QChart, QChartView, QPieSeries
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QFont
CustomObjectRole = QtCore.Qt.UserRole + 1
import pickle
from collections import deque
from PyQt5.QtWidgets import QHBoxLayout,QPushButton,QFileDialog,QLabel,QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsPixmapItem, QGraphicsRectItem, QGraphicsTextItem, QGraphicsView, QGraphicsScene, QMessageBox,QProgressBar,QVBoxLayout,QWidget
from openai import AzureOpenAI
load_dotenv()
api_base = os.getenv("API_BASE")
api_key= os.getenv("AZURE_OPENAI_API_KEY")
deployment_name = os.getenv("DEPLOYMENT_NAME")
api_version = os.getenv("API_VERSION") # this might change in the future
#print(image)
from cloud_emulator.lambda_makespan_cost import compute_lambda_latency_and_cost
from cloud_emulator.s3_makespan_cost import compute_s3_latency_and_cost
from cloud_emulator.dynamodb_makespan_cost import compute_dynamodb_latency_and_cost
from cloud_emulator.retail_workflow_makespan_cost import compute_eks_latency_cost,compute_elasticache_latency_cost

client = AzureOpenAI(
    api_key=api_key,  
    api_version=api_version,
    base_url=f"{api_base}/openai/deployments/{deployment_name}",
)


import sys
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableView, QVBoxLayout, QPushButton,
    QLineEdit, QComboBox, QWidget, QHBoxLayout, QDialog
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QStandardItemModel, QStandardItem


class DataFrameViewer(QMainWindow):
    def __init__(self, df, parent=None):
        super().__init__(parent)
        self.df = df
        self.filtered_df = df.copy()  # Copy for modifications
        self.initUI()

    def initUI(self):
        self.setWindowTitle("DataFrame Viewer")
        self.resize(1200, 600)  # Set wider width for the table

       # Central widget
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)

        # Main layout
        self.layout = QVBoxLayout(self.central_widget)


        # Table view
        self.table_view = QTableView()
        self.table_view.setWordWrap(True)  # Enable word wrap
        self.table_view.resizeColumnsToContents()  # Adjust column width
        self.table_view.horizontalHeader().setStretchLastSection(True)  # Stretch the last column
        self.layout.addWidget(self.table_view)

        # Controls for filtering and sorting
        controls_layout = QHBoxLayout()

        # Filter controls
        self.filter_column_selector = QComboBox()
        self.filter_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.filter_column_selector)

        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("Filter value")
        controls_layout.addWidget(self.filter_input)

        self.filter_button = QPushButton("Filter")
        self.filter_button.clicked.connect(self.apply_filter)
        controls_layout.addWidget(self.filter_button)

        # Sort controls
        self.sort_column_selector = QComboBox()
        self.sort_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.sort_column_selector)

        self.sort_button = QPushButton("Sort")
        self.sort_button.clicked.connect(self.apply_sort)
        controls_layout.addWidget(self.sort_button)

        self.layout.addLayout(controls_layout)

        # Display the table
        self.load_table(self.df)

        # Set the layout
        #self.setLayout(layout)

    def load_table(self, df):
        """Load a DataFrame into the QTableView."""
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(df.columns)

        # Set bold font for the header
        font_bold = QFont()
        font_bold.setBold(True)

        for col in range(len(df.columns)):
            model.setHeaderData(col, Qt.Horizontal, df.columns[col], Qt.DisplayRole)
            model.setHeaderData(col, Qt.Horizontal, font_bold, Qt.FontRole)

        # Populate the table
        for row in df.itertuples(index=False):
            items = [QStandardItem(str(item)) for item in row]
            for item in items:
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # Align text left and vertically centered
                item.setFont(QFont("Arial", 10))  # Set font size for cells
            model.appendRow(items)

        # Set the model to the table view
        self.table_view.setModel(model)

        # Adjust row and column sizes
        self.table_view.resizeRowsToContents()  # Adjust row height for word wrap
        self.table_view.horizontalHeader().setDefaultSectionSize(200)  # Set default column width

    def apply_filter(self):
        """Filter the table based on the selected column and input value."""
        column = self.filter_column_selector.currentText()
        value = self.filter_input.text()
        self.filtered_df = self.df[self.df[column].astype(str).str.contains(value, na=False, case=False)]
        self.load_table(self.filtered_df)

    def apply_sort(self):
        """Sort the table based on the selected column."""
        column = self.sort_column_selector.currentText()
        self.filtered_df = self.filtered_df.sort_values(by=column, ascending=True)
        self.load_table(self.filtered_df)


'''
class DataFrameViewer(QMainWindow):
    def __init__(self, df, parent=None):
        super().__init__(parent)
        self.df = df
        self.filtered_df = df.copy()  # Copy for modifications
        self.initUI()

    def initUI(self):
        self.setWindowTitle("Grouped Bar Graph and Data Table in PyQt5")
        self.setGeometry(20, 20, 800, 600)
        self.setWindowTitle("DataFrame Viewer")
        self.resize(800, 600)

        # Central widget
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)

        # Main layout
        self.layout = QVBoxLayout(self.central_widget)

        # Table view
        self.table_view = QTableView()
        self.layout.addWidget(self.table_view)

        # Controls for filtering and sorting
        controls_layout = QHBoxLayout()

        # Filter controls
        self.filter_column_selector = QComboBox()
        self.filter_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.filter_column_selector)

        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("Filter value")
        controls_layout.addWidget(self.filter_input)

        self.filter_button = QPushButton("Filter")
        self.filter_button.clicked.connect(self.apply_filter)
        controls_layout.addWidget(self.filter_button)

        # Sort controls
        self.sort_column_selector = QComboBox()
        self.sort_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.sort_column_selector)

        self.sort_button = QPushButton("Sort")
        self.sort_button.clicked.connect(self.apply_sort)
        controls_layout.addWidget(self.sort_button)

        self.layout.addLayout(controls_layout)

        # Display the table
        self.load_table(self.df)

    def load_table(self, df):
        """Load a DataFrame into the QTableView."""
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(df.columns)

        for row in df.itertuples(index=False):
            items = [QStandardItem(str(item)) for item in row]
            model.appendRow(items)

        self.table_view.setModel(model)

    def apply_filter(self):
        """Filter the table based on the selected column and input value."""
        column = self.filter_column_selector.currentText()
        value = self.filter_input.text()
        self.filtered_df = self.df[self.df[column].astype(str).str.contains(value, na=False, case=False)]
        self.load_table(self.filtered_df)

    def apply_sort(self):
        """Sort the table based on the selected column."""
        column = self.sort_column_selector.currentText()
        self.filtered_df = self.filtered_df.sort_values(by=column, ascending=True)
        self.load_table(self.filtered_df)

'''
class GraphicView(QGraphicsView):  #Canvas for inserting nodes and creating pipeline
    classmapping={}
    def __init__(self):
        super().__init__()

        self.scene = QGraphicsScene()
        self.setScene(self.scene)       
        self.setSceneRect(0, 0, 2400, 2000)
        self.shape = None
        self.cats={}
        self.prevShape = None
        self.connectorActive = False
        self.setViewportUpdateMode(QGraphicsView.FullViewportUpdate)
        self.configs = []
        self.tfrs = []
        self.setAcceptDrops(True)
        self.adjacency_matrix = []
        self.selectedservs=[]
        self.vpcbuttons=[]
        self.archtype=None
        self.mappings={}
        self.vpclist={}
        self.vpcsquares={}
        #self.vpcbuttons=[]
        self.dotted_pixmap = self.create_dotted_pixmap(dot_color=QColor(0x888888), dot_radius=2, dot_spacing=20, background_color = QColor(0xf0f0f0))
        self.set_dotted_background()

    def create_dotted_pixmap(self, dot_color, dot_radius, dot_spacing, background_color):
         # Create a pixmap with the dot and transparent background
         dot_size = dot_spacing  # Tile the dot + space
         pixmap = QPixmap(dot_size, dot_size)
         pixmap.fill(Qt.transparent)  # Transparent background for the tile

         painter = QPainter(pixmap)
         painter.setRenderHint(QPainter.Antialiasing)  # For smoother dots
         painter.setBrush(dot_color)
         painter.setPen(Qt.NoPen)
         center = dot_size // 2
         painter.drawEllipse(center - dot_radius // 2, center-dot_radius //2 , dot_radius, dot_radius)  # Draw the dot at the center of tile
         painter.end()

         return pixmap

    def set_dotted_background(self):
         # Prepare the final tiled pixmap for the entire widget
        widget_width = self.viewport().width()
        widget_height = self.viewport().height()

        tiled_pixmap = QPixmap(widget_width, widget_height)
        tiled_pixmap.fill(QColor(0xffffff)) # fill with background color
        painter = QPainter(tiled_pixmap)
        dot_size = self.dotted_pixmap.width()
        for y in range(0, widget_height, dot_size):
            for x in range(0, widget_width, dot_size):
                painter.drawPixmap(x, y, self.dotted_pixmap)
        painter.end()

        self.setBackgroundBrush(QtGui.QBrush(tiled_pixmap))


    def resizeEvent(self, event):
        # Update the dotted background pixmap on resizing
        self.set_dotted_background()
        super().resizeEvent(event)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().hasImage():
            event.acceptProposedAction()  # Accept the drop event
        else:
            event.ignore()

    def dropEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().hasImage():
            text = event.mimeData().text()
            image = event.mimeData().imageData()
            icon_label = QLabel(text, self)
            icon_label.setPixmap(QPixmap.fromImage(image))
            icon_label.move(event.pos())
            event.setDropAction(Qt.MoveAction)
            event.accept()
        else:
            event.ignore()

    def addObjectModel(self,model):
        self.shape = ModelShape(model)

    # def addStorage(self,storageType):
    #     self.shape = StorageShape(storageType)

    def addObject(self,text:str,a,b,cost,latency,type1):
        self.shape = Shape(text,a,b,cost,latency,type1,diagram)
        Widgets.widget5.clearSelection()

    def addObject(self,shape):
        self.shape = shape
        #CSWidgets.widget5.clearSelection()

    from collections import defaultdict

    def longest_path_next_node(adj_list, start_node):
    # Cache for storing the longest path from each node
      longest_path_cache = {}

      def dfs(node):
          """DFS to calculate the longest path from the current node."""
          if node in longest_path_cache:
              return longest_path_cache[node]

          if not adj_list[node]:  # If there are no outgoing edges
              longest_path_cache[node] = (0, None)  # No next node
              return longest_path_cache[node]

          max_length = 0
          next_node = None

          for neighbor in adj_list[node]:
              path_length, _ = dfs(neighbor)
              if path_length + 1 > max_length:
                  max_length = path_length + 1
                  next_node = neighbor

          longest_path_cache[node] = (max_length, next_node)
          return longest_path_cache[node]

      # Compute the longest path from the start_node
      _, next_node = dfs(start_node)
      return next_node

    
    def bfs_with_parents(self,adj_list, start_node):
      # Initialize a queue for BFS and a list to store the result
      queue = deque([start_node])
      visited = {start_node}  # Set to track visited nodes
      result = [(start_node, None)]  # Start node has no parent
      
      while queue:
          node = queue.popleft()
          
          # Explore all neighbors of the current node
          for neighbor in adj_list.get(node, []):
              if neighbor not in visited:  # If neighbor hasn't been visited
                  visited.add(neighbor)  # Mark as visited
                  result.append((neighbor, node))  # Append (neighbor, parent)
                  queue.append(neighbor)  # Add the neighbor to the queue
                  
      return result

    

    def longest_path_lengths(self, graph, node, memo):
        if node in memo:
            return memo[node]
        
        if not graph.get(node):  # Leaf node
            memo[node] = 0
            return 0
        
        max_depth = max(self.longest_path_lengths(graph, child, memo) for child in graph[node]) + 1
        memo[node] = max_depth
        return max_depth

    def bfs_ordered(self, graph, start):
        print("bfsordered")
        print(graph)
        print(start)
        memo = {}

        # Compute longest path lengths for sorting
        for node in graph:
            self.longest_path_lengths(graph, node, memo)
        
        visited = set()
        queue = deque([(start, None)])  # (node, parent)
        result = []

        while queue:
            node, parent = queue.popleft()
            if node in visited:
                continue
            visited.add(node)
            result.append((node, parent))

            if node in graph:
                sorted_children = sorted(graph[node], key=lambda x: memo[x], reverse=True)
                queue.extend((child, node) for child in sorted_children)

        # If there are unreachable nodes, return None
        #if len(visited) < len(graph):
        #    return None

        return result


    def possibilities(self,currentspotx,currentspoty):
        poss=[]
        if currentspotx==1670 and currentspoty<1820:
          poss.append((currentspotx+150,currentspoty))          
          poss.append((currentspotx,currentspoty+150))
          poss.append((currentspotx+150,currentspoty+150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty+150))         
          if currentspoty-150>=0:
            poss.append((currentspotx+150,currentspoty-150))
            poss.append((currentspotx,currentspoty-150))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ####################################3
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        elif currentspotx<1820 and currentspoty<1820:
          poss.append((currentspotx+150,currentspoty))
          poss.append((currentspotx+150,currentspoty+150))
          poss.append((currentspotx,currentspoty+150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty+150))        
          if currentspoty-150>=0:
            poss.append((currentspotx+150,currentspoty-150))
            poss.append((currentspotx,currentspoty-150))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ######################################
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        elif currentspotx>=1820 and currentspoty<1820:
          poss.append((currentspotx,currentspoty+150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty+150))
          if currentspoty-150>=0:
            poss.append((currentspotx,currentspoty-150))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          poss.append((currentspotx+150,currentspoty))
          poss.append((currentspotx+150,currentspoty+150))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ######################################
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        elif currentspotx>=1820 and currentspoty>=1820:
          poss.append((currentspotx,currentspoty))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          poss.append((currentspotx+150,currentspoty))
          if currentspoty-150>=0:
            poss.append((currentspotx+150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ######################################
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        return poss

    def plotOnCanvas2(self, adj_list,shapedict):
        shapeList = []
        occupiedspots=[]
        putted=[]
        #lenOfShapes = len(collShape)
        #adj_list=self.edges_to_adjacency_list(collShape)
        currentspotx=160
        currentspoty=160
        print(adj_list)
        userstr="User"
        if userstr not in shapedict:
           userstr="User_0"
        print(userstr)
        ordering=self.bfs_ordered(adj_list, userstr)
        
        print("ooooooooooooorrrrrddddddeeeeerrriiiiiiinnnnnnnnngggggg")
        print(ordering)
        self.scene.addItem(shapedict[userstr])
        text_item=QGraphicsTextItem(shapedict[userstr].name.replace("Amazon ","").replace("AWS ",""))
        text_item.setDefaultTextColor(QColor(Qt.black))
        
        text_item.setPos(QPointF(currentspotx-5,currentspoty+55))
        text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
        text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
        self.scene.addItem(text_item)
        shapedict[userstr].textItem = text_item
        shapedict[userstr].setPos(QPointF(currentspotx,currentspoty))
        shapedict[userstr].setFlag(QGraphicsTextItem.ItemIsSelectable,True)
        shapedict[userstr].setFlag(QGraphicsTextItem.ItemIsMovable,True)
        putted.append(shapedict[userstr])
        occupiedspots.append((currentspotx,currentspoty))
        #currentspotx+=150
        baapxymap={userstr:(currentspotx,currentspoty)}
        CSWidgets.button5.setEnabled(True) 
        CSWidgets.buttonsave.setEnabled(True)
        CSWidgets.buttonClear.setEnabled(True)
        CSWidgets.costlatencybutton.setEnabled(True)
        CSWidgets.selectbutton.setEnabled(True)
        #CSWidgets.deploybutton.setEnabled(True)
        CSWidgets.modelsBox.setEnabled(True)
        prevparent=None
        uniqordering=[]
        print(ordering)
        for pairu in ordering:
           if pairu not in uniqordering:
              uniqordering.append(pairu)
        print(uniqordering)
        for pair in uniqordering:
            #print(x.name)
            if userstr == pair[0]:
               continue
            
            #if prevparent!=pair[1] and pair[1]!=None:
            currentspotx,currentspoty=baapxymap[pair[1]]
            print(pair)
            #print(shapedict)
            
            
            print("baaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaap")
            print((currentspotx,currentspoty))
            posses=self.possibilities(currentspotx,currentspoty)
            print("possibilituessssssssssssssss")
            print(posses)
            pscount=0
            currcurrentspotx,currcurrentspoty=posses[pscount]
            print("spottingggggggggggggggggggggggggggggggggg")
            while (currcurrentspotx,currcurrentspoty) in occupiedspots:
              #print(pair)
              print(occupiedspots)
              print((currcurrentspotx,currcurrentspoty))
              pscount+=1
              currcurrentspotx,currcurrentspoty=posses[pscount]
              print((currcurrentspotx,currcurrentspoty))
            print("ultimatelyyyyyyyyyyyyyyy")
            print((currcurrentspotx,currcurrentspoty))
            if shapedict[pair[0]] not in putted:
              self.scene.addItem(shapedict[pair[0]])
              shapedict[pair[0]].setPos(QPointF(currcurrentspotx,currcurrentspoty))
              shapedict[pair[0]].setFlag(QGraphicsTextItem.ItemIsSelectable,True)
              shapedict[pair[0]].setFlag(QGraphicsTextItem.ItemIsMovable,True)
              text_item=QGraphicsTextItem(shapedict[pair[0]].name.replace("Amazon ","").replace("AWS ",""))
              text_item.setDefaultTextColor(QColor(Qt.black))
              
              text_item.setPos(QPointF(currcurrentspotx-15,currcurrentspoty+55))
              text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
              text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
              shapedict[pair[0]].textItem = text_item
              self.scene.addItem(text_item)
              putted.append(shapedict[pair[0]])
            occupiedspots.append((currcurrentspotx,currcurrentspoty))
            if pair[1]!=None:
              arrow = Arrow(shapedict[pair[1]],shapedict[pair[0]],True)
              self.adjacency_matrix.append([shapedict[pair[1]],shapedict[pair[0]]])
              self.scene.addItem(arrow)
            baapxymap[pair[0]]=currcurrentspotx,currcurrentspoty
            prevparent=pair[1]
    def edges_to_adjacency_list(self,edges):
          adjacency_list = {}  # Stores the final adjacency list
          in_degree = {}  # Tracks incoming edges for cycle detection

          # Step 1: Build initial adjacency list and in-degree count
          print("Input edges:", edges)
          for u, v in edges:
              if u not in adjacency_list:
                  adjacency_list[u] = []
                  in_degree[u] = 0  # Initialize in-degree

              if v not in adjacency_list:
                  adjacency_list[v] = []
                  in_degree[v] = 0  # Initialize in-degree

              # Prevent self-loops (u → u)
              if u == v:
                  continue  

              # Add edge if it doesn't create a duplicate
              if v not in adjacency_list[u]:
                  adjacency_list[u].append(v)
                  in_degree[v] += 1  # Increase in-degree for cycle detection

          print("Initial Adjacency List:", adjacency_list)
          '''
          # Step 2: Detect and remove cycles using Kahn's Algorithm
          queue = deque([node for node in in_degree if in_degree[node] == 0])  # Start with nodes having no incoming edges
          acyclic_adjacency_list = {node: [] for node in adjacency_list}  # Initialize with all nodes

          visited_nodes = set()

          while queue:
              node = queue.popleft()
              visited_nodes.add(node)

              for neighbor in adjacency_list[node]:
                  in_degree[neighbor] -= 1  # Reduce in-degree
                  if in_degree[neighbor] == 0:
                      queue.append(neighbor)  # Add new independent nodes
                  acyclic_adjacency_list[node].append(neighbor)
          '''
      # Step 3: Handle cycles
      #remaining_nodes = set(adjacency_list.keys()) - visited_nodes
      #if remaining_nodes:
      #    print(f"Cycle detected! Nodes involved: {remaining_nodes}")
      #    return adjacency_list  # Returning the original adjacency list to preserve all nodes

      #return acyclic_adjacency_list
          acyclic_adjacency_list=adjacency_list
          correct_acyclic_adjacency_list={}
          dones=[]
          print("origgg")
          print(acyclic_adjacency_list)
          donecounter={}
          for k,v in acyclic_adjacency_list.items():
              newvals=[]
              for vals in v:
                  if k!=vals:
                      if vals not in dones:
                          newvals.append(vals)
                      else:
                          counter=donecounter[vals]+1
                          newvals.append(vals+"_"+str(counter))
                          donecounter[vals]+=1
                  
              if len(newvals)>0:
                  nk=k
                  if k in dones:
                      nk=k+"_"+str(donecounter[vals])
                  correct_acyclic_adjacency_list[k]=newvals
                  dones.append(k)
                  donecounter[k]=0
          print(acyclic_adjacency_list)
          print(correct_acyclic_adjacency_list)
          return correct_acyclic_adjacency_list  # Returns a clean, cycle-free adjacency list

    def plotGraph(self,network): 
        
        #serviceList = [7,10,11,97,98]#t2.toPlainText().splitlines() 
        rownum = 0 
        #serviceList = self.t2.toPlainText().splitlines() 
        serviceList = network.splitlines()
        edgeslist=[]
        for ee in serviceList:
            pair=ee.split("-")
            edgeslist.append((pair[0].strip(),pair[1].strip()))
        adj_list=self.edges_to_adjacency_list(edgeslist)
        #collShape = [] 
        print("==============================================")
        print(adj_list)
        shapedict={}
        for node, neighbors in adj_list.items():
            import builtins
            newneighbors = builtins.list(neighbors)
            #newneighbors=list(neighbors)
            newneighbors.append(node)
            print("newneighbours")
            print(newneighbors)
            for servi in newneighbors:
                print("servi")
                print(servi)
                if servi in shapedict:
                   continue
                rownum = 0
                print("serviiiii")
                print(servi)
                serv=servi.split("_")[0]
                for iterator in awslist: 
                    rownum += 1 
                    
                    if serv.replace("- ", "") == iterator or (serv!="User" and " ".join(serv.split(" ")[1:]).strip() in iterator):
                        print("Here in PlotGraph 1 "+serv) 
                        print(rownum)
                        print(iterator)
                        shape = Shape("CSP",50,50,0,0,rownum)
                        shapedict[servi]=shape
                        #collShape.append(shape) 
                        break
                    elif serv.replace("- ", "") == iterator and (serv=="User"):
                        print("Here in PlotGraph 2 "+serv) 
                        print(rownum)
                        print(iterator)
                        shape = Shape("CSP",50,50,0,0,rownum)
                        shapedict[servi]=shape
                        #collShape.append(shape) 
                        break
                    
        print(adj_list)
        print(shapedict)
        import sys
        #sys.exit()
        self.plotOnCanvas2(adj_list,shapedict) #for x in collShape:


    def loadImage(self):
        import base64
        import openai
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        fileName, _ = QFileDialog.getOpenFileName(self,"Load Logic", ".","PNG Files (*.png)", options=options)
        with open(fileName,'rb') as file:
          encoded_image = base64.b64encode(open(fileName, 'rb').read()).decode('ascii')
        #encoded_image_example_real = base64.b64encode(open("TMCexamplereal.png", 'rb').read()).decode('ascii')
        #encoded_image = base64.b64encode(open(image, 'rb').read()).decode('ascii')
        client = openai.AzureOpenAI(
            api_key="8XHopHUcg6hikApAMUNRvXmzDiZTOuA9XBi9DfwbpiLpZ8oUHZSZJQQJ99BBACYeBjFXJ3w3AAABACOGDAY9",  # Replace with your actual Azure OpenAI API key
            api_version="2024-02-15-preview",  # GPT-4o requires this version
            azure_endpoint="https://sharodmaple.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview"  # Example: https://your-resource.openai.azure.com
        )
        def getresponse(query, outtokens=500):
          """Processes both text and an image using Azure OpenAI GPT-4o."""
          response = client.chat.completions.create(
              model="gpt-4o",  # Ensure this matches your deployed model name
              messages=[
                  {"role": "user", "content": "You are an expert AWS cloud architect. You can see an image of a cloud architechture and read and comprehend it correctly. You can also identify multiple occurances of a cloud service in the image."},
                  {"role": "user", "content": [
                      {"type": "text", "text": query},  # User's query
                      {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"}}  # Image
                  ]}
              ],
              max_tokens=outtokens
          )
          
          return response.choices[0].message.content
        print(awslist)
        nodes=getresponse("List all the cloud services and non cloud inputs present in the diagram. By non cloud inputs I mean objects that provides inputs by arrows to the cloud services by line starting from it, or receives output from cloud services by line coming into it along with a arrow head. Take care to output all non cloud input and output objects. Do not write any more text. Always in the image there must be an initial input coming into the cloud architecture and output going out of the could architecture. Always model this input and output as non cloud service 'User'. So your reply must and must contain this. Dont miss non cloud services. Do not provide any explanation. Do not write any prefix sentences. Do not provide numbering of the services. Just write the service names one after the other seperated by newline only, not comma etc. Predict the unique set of cloud services in the image. The services you predict, should be always from the valid AWS services list given below. Do not predict a service that is not in the valid AWS services list. And the text you predict should be ditto from the valid AWS services list. Do not change a word, or do not shorten the text of any service. The list is:\n "+"\n".join(awslist) ,500).split("\n")
        newnodes=[]
        print(nodes)
        for n in nodes:
          if "cloud services" in n.lower() or "non-cloud" in n.lower() or len(n.strip())==0:
            continue
          newnodes.append(n.replace("- ","").strip())
        nodes=newnodes
        nodecount=getresponse("For each of the cloud service only in the list, mention the count or number of them occuring in the image. So basically you have to say for each of the text in the list below, how many times that text occur in the image. The output format should be each service in each line, followed by a dash '-' and then its count. Do not write any other text. Do not write any prefix or suffix text. The list is:\n "+"\n".join(nodes)).split("\n")
        newnewnodelist=[]
        for nodu in nodecount:
          if int(nodu.split("-")[-1])==1:
            newnewnodelist.append(nodu.split("-")[0])
          else:
             for intu in range(int(nodu.split("-")[-1])):
                newnewnodelist.append(nodu.split("-")[0]+"_"+str(intu))
        print("nnnnnnnnnewwwwwwwwwwwnnnnnnnnnnnewwwwwwwww")
        print(newnewnodelist)
        tons=getresponse("The services in the diagram are directly connected by a single arrow, originating from it, in 1 step to 1 other service in the diagram. Remember that there is always only a single target service. By an arrow originating from one service, I mean that the tail of the arrow (not having the arrow head), is attached to the other service. While the head of the arrow containing the arrow head in attached to those services or box in the diagram. Be very very careful about the direction of arrows. One service may have multiple arrows originating from it. I want a list of all such arrows in the image, outputted as pairs of services. A service can occur multiple times in the image. For that, the list will have repetitions of the same servive followed by an underscore and a unique count. So match a service in the image with a service followed by an underscore and respective counter in the list. Thus while outputting the service pairs take care to check which service followed by underscore and counter is part of which arrow. Dont mix this up. For example Amazon EC2, if there are multiple EC2, make sure you note from which EC2, each arrow is originating from or ending to.  Take care and mention all of them. Always in the image there must be an initial input coming into the cloud architecture and output going out of the could architecture. This input and out is always the 'User' service in the list, maybe followed by underscore and counter. So the must and absolutely must be an input arrow in your replies from one of the 'User' service and an output to the same or other 'User' service.  Do not write any more text or dashes or numbers. Do not provide any explanation. I do not want to know anything extra. Just output the respective input service followed by '-' and followed by target services. Each and every service mentioned in the list must feature in your edges list. Dont miss anyone. Dont miss even a service which is mentioned multiple times followed by underscore and counter. For example: if there is Amazon EC2_0 and Amazon EC2_1 in the list. Both must and definitely must feature in your output. Dont miss one or more of them. Take very close care that, the graph you thus output, should be fully connected. This means that, in the graph you output, ther should always be a path from the starting service which is always User_0. Take very good care of this. The complete service list is: "+str(newnewnodelist),1000)
        tonscorr=""
        for ton in tons.split("\n"):
           if "-" in ton:
              tonscorr+=ton+"\n"
        print(tonscorr)
        self.plotGraph(tonscorr)


    def plotOnCanvas(self, collShape):
        print("Enered into Plotting")
        xCord = 50
        yCord = 100
        shifter = 0
        xcounter=0
        xsign=1
        ysign=1
        shapeList = []
        lenOfShapes = len(collShape)
        for x in collShape:
            print(x.name)
            self.scene.addItem(x)
            x.setPos(QPointF(xCord,yCord))
            if xcounter>0 and xcounter%4==0:
              xsign=xsign*-1
              yCord+=150
            else:
              xCord = xCord + 150*xsign
            xcounter+=1
            #yCord=yCord+(30*ysign)
            #ysign=ysign*-1
            shapeList.append(x)
            print(self.scene.items().__len__())
            print(self.scene.items())
            length = self.scene.items().__len__()
            if length <= 2:
                continue
            if length > 2 and length <= 4:
                #arrow = Arrow(self.scene.itemAt(self.scene.items().__len__()-2),self.scene.itemAt(self.scene.items().__len__()))
                arrow = Arrow(self.scene.items()[length-1],self.scene.items()[length-3],True)
                #arrow = Arrow(self.scene.items()[1],self.scene.items()[3],True)
                self.scene.addItem(arrow)
                print("------IF" + str(self.scene.items().__len__()))
            else:
                arrow = Arrow(self.scene.items()[length-(2+(3*shifter))],self.scene.items()[length-(4+(3*shifter))],True)
                self.scene.addItem(arrow)
                print("------REST"+ str(self.scene.items().__len__()))
                shifter = shifter + 1
            
            '''
            elif length > 4 and length <= 7:
                arrow = Arrow(self.scene.items()[length-2],self.scene.items()[length-4],True)
                self.scene.addItem(arrow)
                print("------ELSE")
            elif length > 7 and length <= 11:
                arrow = Arrow(self.scene.items()[length-5],self.scene.items()[length-7],True)
                self.scene.addItem(arrow)
                print("------ELSE222")
            elif self.scene.items().__len__() > 11:
                arrow = Arrow(self.scene.items()[length-8],self.scene.items()[length-10],True)
                self.scene.addItem(arrow)
                print("------ELSE222")
            else:
                arrow = Arrow(self.scene.items()[length-(2+(3*shifter))],self.scene.items()[length-(4+(3*shifter))],True)
                self.scene.addItem(arrow)
                print("------REST")
            '''    
            #shifter = shifter + 1
        arrow = Arrow(self.scene.items()[length-(2+(3*shifter)-1)],self.scene.items()[length-(4+(3*shifter)-1)],True)
        #arrow = Arrow(self.scene.items()[length-10],self.scene.items()[length-12],True)
        self.scene.addItem(arrow)

        for i in range(lenOfShapes):
          text_item=QGraphicsTextItem(shapeList[i].name.replace("Amazon ","").replace("Simple Storage System (S3)","Amazon S3"))
          text_item.setDefaultTextColor(QColor(Qt.black))
          text_x=shapeList[i].x()-(shapeList[i].a)/2
          text_y=shapeList[i].y()+shapeList[i].b+5
          text_item.setPos(QPointF(text_x,text_y))
          text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
          text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
          self.scene.addItem(text_item)
          shapeList[i].textItem = text_item
        '''
        arrow = Arrow(self.scene.items()[1],self.scene.items()[3],True)
        self.scene.addItem(arrow)
        arrow = Arrow(self.scene.items()[3],self.scene.items()[5],True)
        self.scene.addItem(arrow)
        arrow = Arrow(self.scene.items()[5],self.scene.items()[7],True)
        self.scene.addItem(arrow)
        arrow = Arrow(self.scene.items()[7],self.scene.items()[9],True)
        self.scene.addItem(arrow)
        '''

    def mouseDoubleClickEvent(self, event): #add object in self.shape to location clicked, if location is empty
        print("-------------------"+self.shape.name)
        position = self.mapToScene(event.pos())
        grp = ""
        #print("scene length "+str(self.scene.items().__len__()))
        if(self.shape.group is not None):
            grp = self.shape.group
        if(self.scene.items().__len__() < 1):    
            if(self.shape is not None):
                if(self.itemAt(int(position.x()),int(position.y())) is None):
                    if(self.prevShape is None or self.prevShape != self.shape):
                        self.scene.addItem(self.shape)
                        self.shape.setPos(QPointF(position.x(), position.y()))
                        self.scene.addItem(self.shape.proxy)
                        self.shape.proxy.setPos(QPointF(position.x()-10, position.y()))
                        text_item=QGraphicsTextItem(self.shape.name)
                        text_item.setDefaultTextColor(QColor(Qt.black))
                        font = QFont()
                        font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                        text_item.setFont(font)
                        text_x=position.x()
                        text_y=position.y()+self.shape.b+5;
                        text_item.setPos(QPointF(text_x,text_y))
                        text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                        text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                        self.scene.addItem(text_item)
                        self.shape.textItem = text_item
        else:
            if self.shape.group in self.cats:
                # print(self.shape.group in self.cats)
                reply = QMessageBox.question(self, 'Warning', "You have already selected "+self.cats[self.shape.group][0]+" for the purpose of "+self.shape.group+" Would you like to continue?" ,QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    if self.shape is not None:
                        if(self.itemAt(int(position.x()),int(position.y())) is None):
                            if(self.prevShape is None or self.prevShape != self.shape):
                                self.scene.addItem(self.shape)
                                self.cats[self.shape.group] = [self.shape.name]
                                self.shape.setPos(QPointF(position.x(), position.y()))
                                self.scene.addItem(self.shape.proxy)
                                self.shape.proxy.setPos(QPointF(position.x()-10, position.y()))
                                text_item=QGraphicsTextItem(self.shape.name)
                                text_item.setDefaultTextColor(QColor(Qt.black))
                                text_x=position.x()
                                text_y=position.y()+self.shape.b+5;
                                text_item.setPos(QPointF(text_x,text_y))
                                text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                                font = QFont()
                                font.setPointSize(15)  # Increase the font size to 16

                                # Apply the font to the QGraphicsTextItem
                                text_item.setFont(font)
                                # text_item.setFlag(QGraphicsTextItem.ItemIsFocusable,True)
                                text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                                self.scene.addItem(text_item)
                                self.shape.textItem = text_item
            else:
                print(self.shape.group)
                if(self.shape is not None):
                    if(self.itemAt(int(position.x()),int(position.y())) is None):
                        if(self.prevShape is None or self.prevShape != self.shape):
                            self.scene.addItem(self.shape)
                            
                            self.cats[self.shape.group] = [self.shape.name]
                            self.shape.setPos(QPointF(position.x(), position.y()))
                            self.scene.addItem(self.shape.proxy)
                            self.shape.proxy.setPos(QPointF(position.x()-10, position.y()))
                            text_item=QGraphicsTextItem(self.shape.name)
                            text_item.setDefaultTextColor(QColor(Qt.black))
                            text_x=position.x()
                            text_y=position.y()+self.shape.b+5;
                            font = QFont()
                            font.setPointSize(15)  # Increase the font size to 16

                            # Apply the font to the QGraphicsTextItem
                            text_item.setFont(font)
                            text_item.setPos(QPointF(text_x,text_y))
                            text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                            text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                            # print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
                            self.scene.addItem(text_item)
                            self.shape.textItem = text_item
            
            
        CSWidgets.button5.setEnabled(True)
        CSWidgets.buttonsave.setEnabled(True)
        CSWidgets.costlatencybutton.setEnabled(True)
        #CSWidgets.deploybutton.setEnabled(True)
        CSWidgets.selectbutton.setEnabled(True)
        CSWidgets.labelmarquee.setText(" :: Select first and second cloud service and use 'Connector' button to establish connection between them.")
        CSWidgets.buttonClear.setEnabled(True)
        CSWidgets.modelsBox.setEnabled(True)
        CSWidgets.labelmarquee.setText(" :: To identify next suitable cloud service please Connect with AI or select pre-decided service from left side pallete and plot on canvas using double click.")

        #if(self.shape is not None):
        #    if(self.itemAt(int(position.x()),int(position.y())) is None):
        #        if(self.prevShape is None or self.prevShape != self.shape): 
        #            self.scene.addItem(self.shape)
        #            self.shape.setPos(QPointF(position.x(), position.y()))
        #            if(self.shape.type() == QGraphicsRectItem().type()):
        #                self.shape.configs = self.configs
        #                Widgets.widget5.clearSelection()
        #                Widgets.labelmarquee.setText(" Tip :: Select elements on canvas in order and use 'Connector' button to establish connection between them.")
        #            elif(self.shape.type() == QGraphicsEllipseItem().type()):
        #                self.shape.tfrs = self.tfrs
        #                if(self.shape.text == "I"):
        #                    Widgets.labelmarquee.setText(" Tip :: Select required model from 'Applicable Models' left hand side list and insert in canvas.")
        #                    Widgets.domainsBox.setEnabled(True)
        #                    Widgets.domainlist.setEnabled(True)
        #                if(self.shape.text == "O"):
        #                    Widgets.labelmarquee.setText(" Tip :: Select any deployment resource and check prediction numbers.")    
        #            self.prevShape = self.shape
                    
                    
    def addArrow(self,arr=None): #add arrow from first object clicked to second object clicked, connected to 'Connector' button
        items = self.scene.selectedItems()
        print(arr)
        shapeItems = [item for item in items if (isinstance(item,Shape))]
        if(len(shapeItems) == 2):
            if(shapeItems[0].selectedTime < shapeItems[1].selectedTime):
                start_item = shapeItems[0]
                end_item = shapeItems[1]
            else:
                start_item = shapeItems[1]
                end_item = shapeItems[0]
            if((shapeItems[0].type() == QGraphicsRectItem().type()) & (shapeItems[1].type() == QGraphicsRectItem().type())):
                #selector = Shape("",20,20,0,0,"")
                transformer = Shape("T",30,30,0,0,"")
                self.scene.addItem(transformer)
                transformer.tfrs = self.tfrs
                arrow1 = Arrow(start_item,transformer)
                arrow2 = Arrow(transformer,end_item)
                transformer.setPos((start_item.scenePos()+end_item.scenePos())/2)
                self.scene.addItem(arrow1)
                self.scene.addItem(arrow2)
                #Widgets.button4.setEnabled(True)
                Widgets.widget7.setEnabled(True)
                Widgets.add_tfr_button.setEnabled(True)
                Widgets.edit_tfr_button.setEnabled(True)
            else:
                if arr==None:
                  arrow = Arrow(start_item,end_item,True)
                elif not isinstance(arr,Arrow):
                   arrow = Arrow(start_item,end_item,True)
                   arrow.Cost=arr[0]
                   arrow.Latency=arr[1]
                   arrow.attributes=arr[2]
                else:
                   print("arrowwwww")
                   arrow=arr
                   print(arrow.Cost)
                print(arrow)
                self.scene.addItem(arrow)
                self.scene.addItem(arrow.proxy)
                self.adjacency_matrix.append([start_item,end_item,arrow])
                #GraphicView.netattributes.append({"source_region":start_item.region,"destination_region":end_item.region,"data_size":0,"bandwidth":0})
                #GraphicView.netcostlat_matrix.append((0,0))
                # transformer = Shape("",0,0,0,0,1)
                # arrow1 = Arrow(start_item,transformer,False)
                # arrow2 = Arrow(transformer,end_item,True)
                
                # transformer.setPos(start_item.pos1().x(),end_item.pos1().y())
                # self.scene.addItem(arrow1)
                # self.scene.addItem(arrow2)

                if(end_item.type() != QGraphicsRectItem().type()):
                    if(end_item.text == "O"):
                        Widgets.buttonsave.setEnabled(True)
                        Widgets.DR.setEnabled(True)
                        if(Widgets.deploymentMode=="local"):
                            Widgets.widget6.setEnabled(True)
                            Widgets.serverlabel.setEnabled(True)
                            Widgets.buttonconfig.setEnabled(True)


            shapeItems[0].setSelected(False)
            shapeItems[1].setSelected(False)
            for item in items:
                item.setSelected(False)
        #self.calcCostLatency()
    def addArrowopt(self,shape1,shape2,updateadj): #add arrow from first object clicked to second object clicked, connected to 'Connector' button
        #items = self.scene.selectedItems()
        shapeItems = [shape1,shape2]
        print(shapeItems)
        if(len(shapeItems) == 2):
            #if(shapeItems[0].selectedTime < shapeItems[1].selectedTime):
            #    start_item = shapeItems[0]
            #    end_item = shapeItems[1]
            #else:
            start_item = shapeItems[0]
            end_item = shapeItems[1]
            if((isinstance(shapeItems[0],Shape)) & (isinstance(shapeItems[1],Shape))):
                arrow = Arrow(start_item,end_item,True)
                print("o my god...................................")
                self.scene.addItem(arrow) 
                print("tinkujia.........................................")         
                if updateadj:
                  self.adjacency_matrix.append([start_item,end_item,arrow])
                  #GraphicView.netattributes.append({"source_region":start_item.region,"destination_region":end_item.region,"data_size":0,"bandwidth":0})
                  #GraphicView.netcostlat_matrix.append((0,0))
                
        #self.calcCostLatency()
        print("done**************************************************")

    def updateCloudLatency1(self, model, latency):#Created for sake of demo only
        totalcost = 0
        for x in range(self.scene.items().__len__()):
            if(self.scene.items().__getitem__(x).type() == QGraphicsRectItem().type()):
                if(self.scene.items().__getitem__(x).model.Model == model):
                    self.scene.items().__getitem__(x).model.CloudLatency = latency
                    self.scene.items().__getitem__(x).CloudLatency = latency
                    print(self.scene.items().__getitem__(x).model.CloudLatency)
        print('Updated Total Cost - ',totalcost)
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                print('Cloud Latency ',item.model.CloudLatency)
        return totalcost


    def updateCloudLatency(self, model, storage):
        totalcost = 0
        for x in range(self.scene.items().__len__()):
            if(self.scene.items().__getitem__(x).type() == QGraphicsRectItem().type()):
                if(self.scene.items().__getitem__(x).model.Model == model):
                    self.scene.items().__getitem__(x).model.CloudLatency = self.scene.items().__getitem__(x).model.InitialLatency * (self.scene.items().__getitem__(x).model.Memory/storage)
                    self.scene.items().__getitem__(x).CloudLatency = self.scene.items().__getitem__(x).model.InitialLatency * (self.scene.items().__getitem__(x).model.Memory/storage)
                    print(self.scene.items().__getitem__(x).model.CloudLatency)
                    #self.scene.items().__getitem__(x).Cores = cores
                    #self.scene.items().__getitem__(x).Cost *= self.scene.items().__getitem__(x).Cores/self.scene.items().__getitem__(x).InitialCores
                    #self.scene.items().__getitem__(x).Latency *= self.scene.items().__getitem__(x).InitialCores/self.scene.items().__getitem__(x).Cores
        print('Updated Total Cost - ',totalcost)
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                print('Cloud Latency ',item.model.CloudLatency)
        return totalcost
    
    def getTotalCost(self, cores):
        #Calculation of extrapolated Cost and Latency in below FOR LOOP
        for x in range(self.scene.items().__len__()):
            if(self.scene.items().__getitem__(x).type() == QGraphicsRectItem().type()):
                if(self.scene.items().__getitem__(x).model.Cores != cores):
                    self.scene.items().__getitem__(x).model.Cores = cores
                    icost = self.scene.items().__getitem__(x).model.InitialCost
                    ilatency = self.scene.items().__getitem__(x).model.InitialLatency
                    self.scene.items().__getitem__(x).Cost = icost * self.scene.items().__getitem__(x).model.Cores/self.scene.items().__getitem__(x).model.InitialCores
                    self.scene.items().__getitem__(x).Latency = ilatency * self.scene.items().__getitem__(x).model.InitialCores/self.scene.items().__getitem__(x).model.Cores
        
        items = self.scene.items()
        totalcost = 0
        for item in items:
            if(item.type() == QGraphicsRectItem().type() or item.type() == QGraphicsEllipseItem().type()):
                totalcost += item.Cost
        return totalcost

    def keyPressEvent(self, event): #for deleting object from scene. Error: object gets removed but not deallocated
        if event.key() == Qt.Key_Delete:#Backspace:
            items = self.scene.selectedItems()
            for item in items:
                self.scene.removeItem(item)
                if(item.type() != QGraphicsLineItem().type()):
                    for arrow in item.in_arrows:
                        self.scene.removeItem(arrow)
                    for arrow in item.out_arrows:
                        self.scene.removeItem(arrow)
        #self.calcCostLatency()



    def create_dual_pie_chart_window(self,data1, data2):
        """
        Creates a new window displaying two pie charts based on `data1` and `data2`.
        A single legend is shared, and totals for each chart are prominently displayed above their respective pie charts.

        Parameters:
            data1 (dict): A dictionary where keys are text labels for the slices, and values are their numerical values for the first chart.
            data2 (dict): A dictionary where keys are text labels for the slices, and values are their numerical values for the second chart.
        """
        # Validate the inputs
        if not (data1 and data2) or not (data1.keys() == data2.keys()):
            raise ValueError("Both dictionaries must have the same keys.")
        if not all(isinstance(k, str) and isinstance(v, (int, float)) and v > 0 for k, v in data1.items()):
            raise ValueError("Data1 must be a dictionary with string keys and positive numerical values.")
        if not all(isinstance(k, str) and isinstance(v, (int, float)) and v > 0 for k, v in data2.items()):
            raise ValueError("Data2 must be a dictionary with string keys and positive numerical values.")

        # Calculate the totals
        total1 = sum(data1.values())
        total2 = sum(data2.values())

        # Create a new QMainWindow for the pie charts
        pie_window = QMainWindow()
        pie_window.setWindowTitle("Dual Pie Chart Window")
        pie_window.resize(800, 600)

        # Create pie series for each data set
        series1 = QPieSeries()
        series2 = QPieSeries()
        for label, value in data1.items():
            series1.append(label, value)
            series2.append(label, data2[label])

        # Create charts for each series
        chart1 = QChart()
        chart1.addSeries(series1)
        chart1.setTitle("Breakdown of Total 1")
        chart1.legend().setVisible(False)  # Hide individual legends

        chart2 = QChart()
        chart2.addSeries(series2)
        chart2.setTitle("Breakdown of Total 2")
        chart2.legend().setVisible(False)  # Hide individual legends

        # Create a single shared legend
        shared_legend = QChart()
        shared_legend.addSeries(series1)  # Use one of the series for the legend
        shared_legend.legend().setVisible(True)
        shared_legend.legend().setAlignment(Qt.AlignBottom)

        # Create chart views for each chart
        chart_view1 = QChartView(chart1)
        chart_view1.setRenderHint(QPainter.Antialiasing)
        chart_view2 = QChartView(chart2)
        chart_view2.setRenderHint(QPainter.Antialiasing)

        # Create labels to display the totals
        total_label1 = QLabel(f"Total 1: {total1}")
        total_label1.setAlignment(Qt.AlignCenter)
        total_label1.setFont(QFont("Arial", 14, QFont.Bold))

        total_label2 = QLabel(f"Total 2: {total2}")
        total_label2.setAlignment(Qt.AlignCenter)
        total_label2.setFont(QFont("Arial", 14, QFont.Bold))

        # Create an OK button
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(pie_window.close)  # Connect the button to close the window

        # Layouts
        layout = QVBoxLayout()
        
        # Add first pie chart and total
        chart1_layout = QVBoxLayout()
        chart1_layout.addWidget(total_label1)
        chart1_layout.addWidget(chart_view1)

        # Add second pie chart and total
        chart2_layout = QVBoxLayout()
        chart2_layout.addWidget(total_label2)
        chart2_layout.addWidget(chart_view2)

        # Combine the two charts horizontally
        charts_layout = QHBoxLayout()
        charts_layout.addLayout(chart1_layout)
        charts_layout.addLayout(chart2_layout)

        layout.addLayout(charts_layout)
        layout.addWidget(ok_button)  # Add the OK button at the bottom

        # Create central widget
        central_widget = QWidget()
        central_widget.setLayout(layout)
        pie_window.setCentralWidget(central_widget)

        # Show the pie chart window
        pie_window.show()

        return pie_window  # Return the window in case further manipulation is needed


    def calcLat(self,selectedservs,type,arrl,arrc):
      print(type)
      print(self.archtype)
      costdict={}
      latdict={}
      if type=="Monolith":
        self.selectedservs=selectedservs
        tlate=0.0
        tcost=0.0
        print(self.selectedservs)
        for item in self.scene.items():
          if isinstance(item,Shape) and item.name in self.selectedservs :
              print("ggggggggggggggggggg")
              print( item.Latency)
              tlate += item.Latency
              tcost+=item.Cost
              costdict[item.name]=item.Cost
              latdict[item.name]=item.Latency

          '''     
          for x in self.selectedservs:
            for y in self.selectedservs:
                for pair in self.adjacency_matrix:
                  if pair[0].name==x and pair[1].name==y:
                      tlate+=pair[2].Latency
                      tcost+=pair[2].Cost
                      costdict[x+"-"+y+" network"]=pair[2].Cost
                      latdict[x+"-"+y+" network"]=pair[2].Latency
                      '''
        tlate+=arrl
        tcost+=arrc
        for vpc in self.selectedservs:
           if "VPC" in vpc:
              tcost+=float(self.vpclist[int(vpc.split(" ")[-1])]["cost"].split(" ")[0])
              costdict[vpc]=float(self.vpclist[int(vpc.split(" ")[-1])]["cost"].split(" ")[0])
        #global totLateDisp
        CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tlate))+" sec")
        CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tcost))+" $")
        #self.create_dual_pie_chart_window(costdict, latdict)
      else:
        #self.selectedservs=selectedservs
        tlate=0.0
        self.formGroupBox2 = QGroupBox()
        self.formGroupBox2.setWindowTitle("Input Params")
        self.formGroupBox2.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout2 = QFormLayout()

        self.c22 = QLabel("Please enter Architechture Parameters:")
        self.c22.setAlignment(Qt.AlignCenter)
        self.layout2.addRow(self.c22)
        self.label2 = QLabel("Concurrency: ")
        self.text_box2 = QLineEdit("100")
        self.layout2.addRow(self.label2,self.text_box2)
        #self.label2 = QLabel("Workflow: ")
        #self.text_box2 = QComboBox()
        #self.text_box2 = QLineEdit("1")
        #self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("MySQL Instance Type: ")
        self.text_box2 = QLineEdit("db.t3.medium")
        self.text_box2 = QComboBox()
        self.text_box2.addItems(["db.t3.medium","db.t3.large","db.r5.large"])
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("Elasticache Instance Type: ")
        self.text_box2 = QComboBox()
        self.text_box2.addItems(['t3.micro', 't2.micro', 't3.small', 't2.small'])
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("EKS Num Nodes: ")
        self.text_box2 = QLineEdit("1")
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("EKS Instance Type: ")
        self.text_box2 = QLineEdit("m5.large")
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("Postgresql Instance Type: ")
        self.text_box2 = QLineEdit("db.t3.medium")
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("MQ Instance Type: ")
        self.text_box2 = QLineEdit("t3.micro")
        self.layout2.addRow(self.label2,self.text_box2)
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.finallat)
        cancel_button.clicked.connect(self.returner)
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        self.layout2.addRow(HBox)
        self.formGroupBox2.setLayout(self.layout2)
        self.formGroupBox2.show()

    def open_table_viewer(self):
        # Example DataFrame
        elasticache={"1,1":"C3_T1","1,2":"C3_T2","2,1":"C3_T3","2,2":"C3_T4","2,0.5":"C3_T5","2,1.37":"C3_T6","2,2.79":"C3_T7","2,4":"C3_T8","2,8":"C3_T9","2,4":"C3_T10","2,8":"C3_T11","4,16":"C3_T12","2,8":"C3_T13","4,16":"C3_T14","2,8":"C3_T15","4,16":"C3_T16","2,8":"C3_T17","4,16":"C3_T18","2,8":"C3_T19","4,16":"C3_T20","2,16":"C3_T21","4,32":"C3_T22","2,16":"C3_T23","4,32":"C3_T24","2,16":"C3_T25","4,32":"C3_T26","2,16":"C3_T27","4,32":"C3_T28","2,15.25":"C3_T29","4,30.5":"C3_T30"}
        ec2_10={"2,8":"C1_T1","4,16":"C1_T14","8,32":"C1_T15","16,64":"C1_T16","2,16":"C1_T17","4,32":"C1_T18","8,64":"C1_T19"}
        ec2_5={"2,1":"C1_T8","2,2":"C1_T9","2,4":"C1_T10","2,8":"C1_T11","4,16":"C1_T12","8,32":"C1_T13"}
        ec2_l={"1,1":"C1_T2","1,2":"C1_T3","2,4":"C1_T4","2,8":"C1_T5","4,16":"C1_T6","8,32":"C1_T7"}
        #aurora={"2,4":"C2_T1","2,8":"C2_T2","2,16":"C2_T3","2,1":"C2_T4","2,2":"C2_T5","2,2":"C2_T6","2,4":"C2_T7","2,8":"C2_T8","2,8":"C2_T9","4,16":"C2_T10","8,32":"C2_T11","2,8":"C2_T12","4,16":"C2_T13","8,32":"C2_T14","2,8":"C2_T15","4,16":"C2_T16","8,32":"C2_T17","2,8":"C2_T18","4,16":"C2_T19","8,32":"C2_T20","2,1":"C2_T21","4,32":"C2_T22","2,16":"C2_23","4,32":"C2_T24","2,16":"C2_T25","4,32":"C2_T26","2,16":"C2_T27","4,32":"C2_T28"}
        aurora={"2,4":["C2_T1","C2_T7"],"2,8":["C2_T2","C2_T8","C2_T9","C2_T12","C2_T15","C2_T18"],"2,16":["C2_T3","C2_T23","C2_T25","C2_T27"],"2,1":["C2_T4"],"2,2":["C2_T5","C2_T6"],"4,16":["C2_T10","C2_T13","C2_T19","C2_T16"],"8,32":["C2_T11","C2_T14","C2_T17","C2_T20"],"2,1":["C2_T21"],"4,32":["C2_T22","C2_T24","C2_T26","C2_T28"]}
        awslambda={"1,0.5":"C4_T1","2,1":"C4_T2","3,1":"C4_T3","4,2":"C4_T4","5,3":"C4_T5","6,3":"C4_T6","7,4":"C4_T7","8,4":"C4_T8","9,5":"C4_T9","10,6":"C4_T10"}
        dicti={}
        for i in range(self.layoutft.count()):
          item = self.layoutft.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layoutft.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layoutft.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        elasticachevalid=[]
        ec2valid=[]
        lambdavalid=[]
        auroravalid=[]
        for serv in ["ElastiCache","Elastic Kubernetes Service","Lambda","Aurora"]:
          mincpu=0
          maxcpu=0
          minmem=0
          maxmem=0
          em=""
          bm25=""
          faiss=""
          rm=""
          llama=""
          batch=""
          for k,v in dicti.items():
            if "Embedding model (GTE Large)" in k:
              if v!="All":
                em=[v]
              else:
                 em=["A100", "Inferentia","V100"]
            if "BM25" in k:
              if v!="All":
                bm25=[v]
              else:
                bm25=["A100", "CPU","V100"]
            if "FAISS" in k:
              if v!="All":
                faiss=[v]
              else:
                faiss=["A100", "CPU"]
            if "Reranking model" in k:
              if v!="All":
                rm=[v]     
              else:
                rm=["2-A100", "Inferentia","V100", "A100"]      
            if  "LLM LLaMA3-8B" in k:
              if v!="All":
                llama=[v]
              else:
                llama=["2-A100", "AWS Bedrock","Sambanova", "Cerebras","V100","A100","Inferentia"]
            if "Batch Size" in k:
              if v!="All":
                batch=[v]
              else:
                batch=[1,4]
            if serv in k and "Min" in k and "CPU" in k:
              mincpu=v
            if serv in k and "Max" in k and "CPU" in k:
              maxcpu=v
            if serv in k and "Min" in k and "Memory" in k:
              minmem=v
            if serv in k and "Max" in k and "Memory" in k:
              maxmem=v
          if "ElastiCache" in serv:
              cores=["1","2","4"]
              mems=["0.5","1","1.37","2","2.79","4","8","15.25","16","30.5","32"]
          if "Elastic Kubernetes Service" in serv:
              cores=["1","2","4","8","16"]
              mems=["1","2","4","8","16","32","64"]
          if "Lambda" in serv:
              cores=["0.5","1","2","3","4","5","6"]
              mems=["1","2","3","4","5","6","7","8","9","10"]
          if "Aurora" in serv:
              cores=["2","4","8"]
              mems=["1","2","4","8","16","32"]
          print(mincpu)
          print(maxcpu)
          print(cores)
          if mincpu in cores and maxcpu in cores:
            start_index = cores.index(mincpu)
            end_index = cores.index(maxcpu) + 1
            if start_index < end_index:
                valcores= cores[start_index:end_index]
          if minmem in mems and maxmem in mems:
            start_index = mems.index(minmem)
            end_index = mems.index(maxmem) + 1
            if start_index < end_index:
                valmems= mems[start_index:end_index]
          for valc in valcores:
            for valm in valmems:
              if "ElastiCache" in serv:
                  if valc+","+valm in elasticache.keys():
                      elasticachevalid.append(elasticache[valc+","+valm])
              if "Elastic Kubernetes Service" in serv:
                  
                  #print(ec2[valc+","+valm])
                  if valc+","+valm in ec2_10.keys():  
                      ec2valid.append(ec2_10[valc+","+valm])
                  if valc+","+valm in ec2_5.keys():                      
                      ec2valid.append(ec2_5[valc+","+valm])
                  if valc+","+valm in ec2_l.keys():                      
                      ec2valid.append(ec2_l[valc+","+valm])
              if "Lambda" in serv:
                  #print(valc+","+valm)
                  if valm+","+valc in awslambda.keys():
                      #print(valc+","+valm)
                      #print(awslambda[valc+","+valm])
                      lambdavalid.append(awslambda[valm+","+valc])
              if "Aurora" in serv:
                  print(valc+","+valm)
                  if valc+","+valm in aurora.keys():
                      print(valc+","+valm)
                      print(aurora[valc+","+valm])
                      auroravalid.extend(aurora[valc+","+valm])
        
        df = pd.read_excel("costs.xlsx", sheet_name="Retail+LLM")
        print(ec2valid)
        print(auroravalid)
        print(elasticachevalid)
        print(lambdavalid)
        print(df)
        print(df.columns)
        
        filtered_df = df[
            df['EKS Node Instance Type'].isin(ec2valid) &
            df['Mysql Instance Type'].isin(auroravalid) &
            df['Elasticache Instance Type'].isin(elasticachevalid) &
            df['Carts MS Lambda/EC2'].isin(lambdavalid) &
            df["Embedding model (GTE Large)"].isin(em) &
            df["BM25"].isin(bm25) &
            df["FAISS"].isin(faiss) &
            df["Reranking model (Mistral 7B)"].isin(rm) &
            df["LLM LLaMA3-8B"].isin(llama) &
            df["Batch Size"].isin(batch) 
        ]
        print(filtered_df)
        # Create and show the DataFrame viewer window
        self.viewer = DataFrameViewer(filtered_df, self)
        self.viewer.show()
    
    def select(self):
        print("came here...................")
        for item in self.items():
            if isinstance(item, Shape):
               item.proxy.setPos(item.pos().x(),item.pos().y())
            if isinstance(item, Arrow):
                t = 1/3
                x = item.end_item.x() + t * (item.start_item.x() - item.end_item.x())
                y = item.end_item.y() + t * (item.start_item.y() - item.end_item.y())

                item.proxy.setPos(QPointF(x, y).x(),QPointF(x, y).y())
            if isinstance(item, Shape) or isinstance(item, Arrow):
              if not item.proxy.isVisible():
                item.proxy.setVisible(True)
                item.checkbox.setVisible(True)
              else:
                item.proxy.setVisible(False)
                item.checkbox.setVisible(False)
        
        self.vpccheckboxes=[]
        print(self.vpcbuttons)
        for buttonnow in self.vpcbuttons:
          print(buttonnow)
          checkbox = QCheckBox(buttonnow)
          checkbox.move(5, buttonnow.height() // 2 - checkbox.height() // 2)  # 5 px from left, vertically centered
          checkbox.show()
          self.vpccheckboxes.append(checkbox)
            

    def filter_table(self):
        self.formGroupBoxft = QGroupBox()
        self.formGroupBoxft.setWindowTitle("Filter Params")
        self.formGroupBoxft.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layoutft = QFormLayout()

        self.c22 = QLabel("Please enter Filter Parameters:")
        self.c22.setAlignment(Qt.AlignCenter)
        self.layoutft.addRow(self.c22)
        
        currServices=[]
        for item in self.items():
              if isinstance(item, Shape):# and item.name not in currServices:
                  currServices.append(item.name)
        currServices.append("AWS Lambda")
        print(currServices)
        for serv in currServices:
            if serv != "Elastic Load Balancing" and serv != "User":
                
                cores=[]
                mems=[]
                if "ElastiCache" in serv:
                   cores=["1","2","4"]
                   mems=["0.5","1","1.37","2","2.79","4","8","15.25","16","30.5","32"]
                if "Elastic Kubernetes Service" in serv:
                   cores=["1","2","4","8","16"]
                   mems=["1","2","4","8","16","32","64"]
                if "Lambda" in serv:
                   cores=["0.5","1","2","3","4","5","6"]
                   mems=["1","2","3","4","5","6","7","8","9","10"]
                if "Aurora" in serv:
                   cores=["2","4","8"]
                   mems=["1","2","4","8","16","32"]
                if "EC2" not in serv:
                  self.label3 = QLabel(serv+" CPU Cores: Min: ")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(cores)
                  self.label3a = QLabel(serv+" CPU Cores: Max: ")
                  self.text_box1a = QComboBox()
                  self.text_box1a.addItems(cores)
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.layoutft.addRow(self.label3a,self.text_box1a)
                  self.label3 = QLabel(serv+" Memory: Min: ")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(mems)
                  self.label3a = QLabel(serv+" Memory: Max: ")
                  self.text_box1a = QComboBox()
                  self.text_box1a.addItems(mems)
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.layoutft.addRow(self.label3a,self.text_box1a)
                if "EC2" in serv:
                  self.label3 = QLabel("Embedding model (GTE Large):")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","A100", "Inferentia","V100"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("BM25:")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","A100", "CPU","V100"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("FAISS:")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","A100", "CPU"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("Reranking model (Mistral 7B):")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","2-A100", "Inferentia","V100", "A100"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  
                  self.label3 = QLabel("LLM LLaMA3-8B:")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","2-A100", "AWS Bedrock","Sambanova", "Cerebras","V100","A100","Inferentia"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("Batch Size")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","1","4"])
                  self.layoutft.addRow(self.label3,self.text_box1)
        self.ok_button = QPushButton("Submit")
        self.cancel_button = QPushButton("Cancel")
        self.HBox = QHBoxLayout()
        self.HBox.addWidget(self.ok_button)
        self.HBox.addWidget(self.cancel_button)
        self.layoutft.addRow(self.HBox)
        self.ok_button.clicked.connect(self.open_table_viewer)
        self.cancel_button.clicked.connect(self.returner)
        self.formGroupBoxft.setLayout(self.layoutft)
        self.scrollArea = QScrollArea()
        self.scrollArea.setWidget(self.formGroupBoxft)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setFixedSize(500, 500)  # Adjust the size as needed

    # Show the QScrollArea
        self.scrollArea.show()
        #self.formGroupBoxft.show()

    def anotherform(self):
      self.formGroupBox3 = QGroupBox()
      self.formGroupBox3.setWindowTitle("Architecture Mappings")
      self.formGroupBox3.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layout3 = QFormLayout()

      self.c33 = QLabel("Please enter details to map the cloud services to the function it performs and also provide in which node of the EKS are their microservices located.")
      self.c33.setAlignment(Qt.AlignCenter)
      self.layout3.addRow(self.c33)
      #counter=0
      currServices=[]
      for item in self.items():
            if isinstance(item, Shape):# and item.name not in currServices:
                currServices.append(item.name)
      for serv in currServices:
          if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
              self.label3 = QLabel(serv+": ")
              self.text_box3 = QLineEdit("")
              self.layout3.addRow(self.label3,self.text_box3)
              #counter+=1
      for serv in currServices:
          if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
              self.label3 = QLabel(serv+" EKS Node No.: ")
              self.text_box3 = QLineEdit("")
              self.layout3.addRow(self.label3,self.text_box3)
              #counter+=1
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.anotherform2)
      cancel_button.clicked.connect(self.returner)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layout3.addRow(HBox)
      self.formGroupBox3.setLayout(self.layout3)
      self.formGroupBox3.show()

    def anotherform2(self):
      dicti={}
      #import Diagram
      currServices=[]
      for item in self.items():
            if isinstance(item, Shape):# and item.name not in currServices:
                currServices.append(item.name)
      for i in range(self.layout3.count()):
        item = self.layout3.itemAt(i)
        if item and isinstance(item.widget(),QLineEdit):
          val = item.widget().text()
          key = self.layout3.labelForField(item.widget()).text()
          dicti[key]=val
        elif item and isinstance(item.widget(),QComboBox):
          key = self.layout3.labelForField(item.widget()).text()
          val = item.widget().currentText()
          dicti[key]=val
      for k,v in dicti.items():
        for serv in currServices:
          if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
            if serv+":" in k:
              self.mappings[serv]=v
              GraphicView.classmapping[serv]=v
            if serv+" EKS Node No.:" in k:
              self.mappings[serv+" EKS Node"]=v
              GraphicView.classmapping[serv+" EKS Node"]=v
      self.formGroupBox3.close()
      self.anotherform3()
    #def anotherform4()
    def anotherform3(self):
        self.formGroupBoxc2 = QGroupBox() 
        self.formGroupBoxc2.setMaximumSize(800, 350)  # Adjusted maximum size for a better fit
        self.formGroupBoxc2.setMinimumSize(200, 100)  # Set a minimum size to ensure content fits
        #binary_group = QButtonGroup(self) 
        self.radio_widgetservs = QWidget(self) 
        radio_layout = QVBoxLayout(self.radio_widgetservs)
        question_layout = QVBoxLayout()
        heading = QLabel("Please select the services for which you want the latency and cost:")

        question_layout.addWidget(heading) 
        wk=[]
        for k,v in self.mappings.items():
          if "EKS" not in k:
            wk.append(k+":"+v)
        numvpc=len(self.vpclist)
        print(numvpc)
        print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
        for p in range(numvpc):
           wk.append("VPC "+str(p+1))
        #for serv in currServices:
        #    if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
        checkboxes_widget = QWidget(self)
        checkbox_layout = QVBoxLayout(checkboxes_widget)

        # Create "Select All" checkbox
        select_all_checkbox = QCheckBox("Select All")
        checkbox_layout.addWidget(select_all_checkbox)

        # Create individual checkboxes for each service
        service_checkboxes = []
        for serv in wk:
            #if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
            service_checkbox = QCheckBox(serv)
            service_checkboxes.append(service_checkbox)  # Store checkbox in dictionary
            checkbox_layout.addWidget(service_checkbox)

        question_layout.addWidget(checkboxes_widget)

        # Connect "Select All" checkbox to toggle other checkboxes
        select_all_checkbox.toggled.connect(lambda checked: self.toggle_all(checked, service_checkboxes))

        
        # Create buttons
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.setserv)
        cancel_button.clicked.connect(self.returner)

        # Horizontal layout for buttons
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        h_layout_widget = QWidget()
        h_layout_widget.setLayout(HBox)

        question_layout.addWidget(h_layout_widget)

        # Set spacing and margins for the layout to make it compact
        question_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        question_layout.setSpacing(5)  # Reduce spacing

        self.formGroupBoxc2.setLayout(question_layout)
        self.formGroupBoxc2.show()


    def anotherform4(self):
      self.formGroupBoxc2 = QGroupBox()
      question_layout = QVBoxLayout()
      #if type == "Monolith":
      heading = QLabel("Please select the services for whom you want the latency to be shown. Please make sure the services you select create a single path. \nThe network latency won't be added otherwise. \nIf you select User, then the cost of user will not be calculated, obviously,. But the netweork cost from and to the user will be added.\nThe order of services are in the order of the arrows.")
      
      question_layout.addWidget(heading) 
      wk=[]
      # Collect services based on the provided diagram
      for item in self.items():
          if isinstance(item, Shape):# and item.name not in currServices:
              wk.append(item.name)
      checkboxes_widget = QWidget(self)
      checkbox_layout = QVBoxLayout(checkboxes_widget)

      # Create "Select All" checkbox
      select_all_checkbox = QCheckBox("Select All")
      checkbox_layout.addWidget(select_all_checkbox)
      numvpc=len(self.vpclist)
      print(numvpc)
      print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
      for p in range(numvpc):
          wk.append("VPC "+str(p+1))
      question_layout.addWidget(checkboxes_widget)
      # Create individual checkboxes for each service
      service_checkboxes = []
      for serv in wk:
          #if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
          service_checkbox = QCheckBox(serv)
          service_checkboxes.append(service_checkbox)  # Store checkbox in dictionary
          checkbox_layout.addWidget(service_checkbox)

      

      # Connect "Select All" checkbox to toggle other checkboxes
      select_all_checkbox.toggled.connect(lambda checked: self.toggle_all(checked, service_checkboxes))

      # Create buttons
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.setserv)
      cancel_button.clicked.connect(self.returner)

      # Horizontal layout for buttons
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      h_layout_widget = QWidget()
      h_layout_widget.setLayout(HBox)

      question_layout.addWidget(h_layout_widget)

      # Set spacing and margins for the layout to make it compact
      question_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
      question_layout.setSpacing(5)  # Reduce spacing

      self.formGroupBoxc2.setLayout(question_layout)
      self.formGroupBoxc2.show()

    def takevpcinput(self):
      self.formGroupBoxvpc = QGroupBox()
      self.formGroupBoxvpc.setWindowTitle("VPC params")
      self.formGroupBoxvpc.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layoutvpc = QFormLayout()

      self.c11 = QLabel("Please enter your VPC Parameters:")
      self.c11.setAlignment(Qt.AlignCenter)
      self.layoutvpc.addRow(self.c11)
      self.label1 = QLabel("No. of connections: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Hours per day: ")
      self.text_box1 = QLineEdit("24")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("No. of NAT Gateways: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Data processed per NAT Gateway(GB/month): ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("No. of In-use public IPV4 addresses: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("No. of Idle public IPV4 addresses: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.fillvpcdetails)
      cancel_button.clicked.connect(self.returnervpc)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layoutvpc.addRow(HBox)
      self.formGroupBoxvpc.setLayout(self.layoutvpc)
      self.formGroupBoxvpc.show()

    def returnervpc(self):
      self.formGroupBoxvpc.close()

    def fillvpcdetails(self):
      self.formGroupBoxvpc.close()
      dicti={}
      for i in range(self.layoutvpc.count()):
        item = self.layoutvpc.itemAt(i)
        if item and isinstance(item.widget(),QLineEdit):
          val = item.widget().text()
          key = self.layoutvpc.labelForField(item.widget()).text()
          dicti[key]=val
        elif item and isinstance(item.widget(),QComboBox):
          key = self.layoutvpc.labelForField(item.widget()).text()
          val = item.widget().currentText()
          dicti[key]=val
      connections=0
      hours=0
      for k,v in dicti.items():
        if "No. of connections:" in k:
          connections=int(v)
        elif "Hours per day:" in k:
          hours=int(v)
        elif "No. of NAT Gateways:" in k:
          nats=int(v)
        elif "Data processed per NAT Gateway(GB/month):" in k:
          natdata=float(v)
        elif "No. of In-use public IPV4 addresses:" in k:
          inuse=float(v)
        elif "No. of Idle public IPV4 addresses:" in k:
          idle=float(v)
      cost=((hours*730)/24)*connections*0.05
      cost+=nats*(32.85+(natdata*0.045))
      cost+=(3.65*int(inuse))+(3.65*int(idle))
      self.vpclist[int(self.vpcval)]={"connections":connections,"hours":hours,"NAT Gateways":nats,"Data processed per NAT Gateway":str(natdata)+" GB/month","No. of In-use public IPV4 addresses":inuse,"No. of Idle public IPV4 addresses":idle,"COST":str(cost)+" USD"}
      self.showvpc()


    def showvpc(self):
       msg = QMessageBox()
       msg.setWindowTitle("VPC Details")
       vpcdict={}
       vpcstr=""
       for k,v in self.vpclist[int(self.vpcval)].items():
          vpcdict[k]=v
          vpcstr+=str(k)+": "+str(v)+"\n\n"
       msg.setText(vpcstr)
       msg.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
       buttons = msg.buttons()
       for button in buttons:
          if button.text() == "OK":
              button.setText("Edit")  # Change the text of the OK button
          if button.text() == "Cancel":
              button.setText("OK")
       returnValue = msg.exec()
       if returnValue == QMessageBox.Ok:
          self.takevpcinput()
         

    def getvpcdetails(self,button):
       print("vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv")
       
       self.vpcval = button.text().split(" ")[-1]
       #self.vpcbuttons.append(button)
       #print(vpcval)
       vpccount=CSWidgets.vpccount
       if int(self.vpcval) not in list(self.vpclist.keys()):
          self.takevpcinput()
       else:
          self.showvpc()

    def setserv2(self,type):
        items = self.scene.items()
        self.selectedservs=[]
        arrc=0
        arrl=0
        for item in items:
            if isinstance(item,Shape) or isinstance(item,Arrow):
               item.proxy.setVisible(False)
            if isinstance(item,Shape)  and item.checkbox.isChecked():
               self.selectedservs.append(item.name)
               item.checkbox.setChecked(False)
            if isinstance(item,Arrow) and item.checkbox.isChecked():
               arrl+=item.Latency
               arrc+=item.Cost
               item.checkbox.setChecked(False)
        counter=1
        for checkboxnow in self.vpccheckboxes:
           print("vpclist.................")
           
           print(self.vpclist)
           if checkboxnow.isChecked() and counter in self.vpclist:
              print(self.vpclist)
              arrc+=float(self.vpclist[counter]["COST"].split()[0])
           counter+=1
           checkboxnow.setVisible(False)
        self.calcLat(self.selectedservs,type,arrl,arrc)
               

    def setserv(self):
      checkboxes = self.formGroupBoxc2.findChildren(QCheckBox)
      checked_checkboxes = [checkbox.text() for checkbox in checkboxes if checkbox.isChecked()]
      if checked_checkboxes is not None: 
          answer = checked_checkboxes
      else: 
          answer = []
          #count+=1 
      print(answer)
      if len(answer)>0:
         self.selectedservs=[]
         for ans in answer:
            if ":" in ans:
              servans=ans.split(":")[0].lower().split(" ")[1:]
              self.selectedservs.append(" ".join(servans).strip())
            else:
              self.selectedservs.append(ans)
      print("sssssssssssssssssssssssssssmkefmkfennnnnnnnnkefnkwemnr")
      print(self.selectedservs)
      self.calcLat(self.selectedservs,self.archtype)
      self.formGroupBoxc2.close()

    def finallat(self):
      dicti={}
      for i in range(self.layout2.count()):
        item = self.layout2.itemAt(i)
        if item and isinstance(item.widget(),QLineEdit):
          val = item.widget().text()
          key = self.layout2.labelForField(item.widget()).text()
          dicti[key]=val
        elif item and isinstance(item.widget(),QComboBox):
          key = self.layout2.labelForField(item.widget()).text()
          val = item.widget().currentText()
          dicti[key]=val
      inputdict={}
      print("dicttttttttttttttttttttttttttttttttttttttttiiiiiiiiii")
      print(dicti)
      for k,v in dicti.items():
        if "Concurrency:" in k:
          inputdict["workload"]=int(v)
        elif "MySQL Instance Type:" in k:
          inputdict["mysql_instance_type"]=v
        elif "Elasticache Instance Type:" in k:
          inputdict["elasticache_instance_type"]=v  
        elif "EKS Num Nodes:" in k:
          inputdict["eks_num_nodes"]=int(v)  
        elif "EKS Instance Type:" in k:
          inputdict["eks_instance_type"]=v 
        elif "Postgresql Instance Type:" in k:
          inputdict["postgresql_instance_type"]=v 
        elif "MQ Instance Type:" in k:
          inputdict["mq_instance_type"]=v 
      #if self.selectedservs[0].isnumeric():
      inputdict["services_list"]=self.selectedservs
      self.formGroupBox2.close()
      print(inputdict)
      outy=compute_eks_latency_cost(inputdict)
      tcost=0
      for vpc in self.selectedservs:
           if "VPC" in vpc:
              tcost+=float(self.vpclist[int(vpc.split(" ")[-1])]["cost"].split(" ")[0])

      CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(outy["latency"]))+" sec")
      CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(outy["cost"]+tcost))+" $")



    def calcCostLatency(self):
        tcost = 0.0
        #tlate = 0.0
        items = self.scene.items()
        for item in items:
            if isinstance(item,Shape):
                print(item.name)
                print(item.Cost)
                tcost += item.Cost
                #tlate += item.Latency
        #print(GraphicView.netcostlat_matrix)
        for pair in self.adjacency_matrix:
           print("XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
           print(pair[2].Cost)
           print(pair[2].Latency)
           tcost+=pair[2].Cost
           #tlate+=pair[2].Latency
                   

        CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tcost))+" $")
        #CSWidgets.totLateDisp.setText(" :: "+'<b><font color="red">{}</font></b>'.format("{:.4f}".format(tlate))+" sec")
        #CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tlate))+" sec")

    


    def calcCloudCost(self): #cost for cloud for all item in scene
        totalsum = 0
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                    totalsum += item.model.CloudCost
                    #print(totalsum,"-",item.model.CloudCost)
        return totalsum

    def calcCloudLatency(self): 
        totalsum = 0
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                    totalsum += item.model.CloudLatency
                    print(totalsum,"-",item.model.CloudLatency,"-",item.CloudLatency)
        return totalsum/2
    
    def updateConfigList(self,config):
        self.configs.append(config)
        for item in self.scene.items():
            if(item.type() == QGraphicsRectItem().type()):
                item.configs = self.configs

    def updateTfrList(self,tfr):
        self.tfrs.append(tfr)
        for item in self.scene.items():
            if(item.type() == QGraphicsEllipseItem().type()):
                item.tfrs = self.tfrs
    
    def removeTfrFromList(self,tfr):
        self.tfrs.remove(tfr)
        for item in self.scene.items():
            if(item.type() == QGraphicsEllipseItem().type()):
                item.tfrs = self.tfrs

    def getNetLatency(self):
        def longestPath(graph):
            ans = 0
            n = len(graph)
            table = [-1] * n

            def dfs(u):
                if table[u] != -1:
                    return table[u]
                p_len = 0
                for v in graph[u]:
                    p_len = max(p_len, v[1] + dfs(v[0]))
                table[u] = p_len
                return p_len

            for i in range(n):
                ans = max(ans, dfs(i))    #graph[u] contains arrays of outgoing nodes; graph is an adjacency list
            return ans

        source = Shape("",10,10,0,0,"") #virtual node connected to all sources
        nodelist = [source]
        items = self.scene.items()
        for item in items:
            if((item.type() == QGraphicsRectItem().type()) | (item.type() == QGraphicsEllipseItem().type())):
                nodelist.append(item)
                #print('Appended : ',item.shape)
                if((item.in_arrows == []) and (item is not source)):
                    Arrow(source,item)

        graph = [[(nodelist.index(arrow.end_item),arrow.end_item.Latency) if arrow.end_item.type() == QGraphicsRectItem().type() or arrow.end_item.type() == QGraphicsEllipseItem().type() else (nodelist.index(arrow.end_item),0) for arrow in node.out_arrows] for node in nodelist]
        #print(graph)
        for arrow in source.out_arrows:
            arrow.end_item.in_arrows = []
        source.out_arrows = []
        return longestPath(graph)

    def getNetLatency2(self):
        source = Shape("",10,10,0,0,"") #virtual node connected to all sources
        nodelist = [source]
        items = self.scene.items()
        print('items lengh ',len(items))
        print(items)
        for item in items:
            if((item.type() == QGraphicsRectItem().type()) | (item.type() == QGraphicsEllipseItem().type())):
                nodelist.append(item)
                print('Appended : ',item.Latency,' - ',item.type())
                if((item.in_arrows == []) and (item is not source)):
                    Arrow(source,item)
        
        lat = 0
        for node in nodelist:
            max = 0
            for arrow in node.out_arrows:
                if(max < arrow.end_item.Latency):
                    max = arrow.end_item.Latency
            #print(max)
        #print('-')
        lat = lat + max
        
        return lat
        
    def getNetCloudLatency(self):
        def longestCloudPath(graph):
            ans = 0
            n = len(graph)
            table = [-1] * n

            def dfsCloud(u):
                if table[u] != -1:
                    return table[u]
                p_len = 0
                for v in graph[u]:
                    p_len = max(p_len, v[1] + dfsCloud(v[0]))
                table[u] = p_len
                return p_len

            for i in range(n):
                ans = max(ans, dfsCloud(i))    #graph[u] contains arrays of outgoing nodes; graph is an adjacency list
            return ans

        source = Shape("",10,10,0,0,"") #virtual node connected to all sources
        nodelist = [source]
        items = self.scene.items()
        #print('items', len(items))
        for item in items:
            if((item.type() == QGraphicsRectItem().type()) | (item.type() == QGraphicsEllipseItem().type())):
                nodelist.append(item)
                #print('Appended : ',item.shape)
                if((item.in_arrows == []) and (item is not source)):
                    Arrow(source,item)

        graph = [[(nodelist.index(arrow.end_item),arrow.end_item.CloudLatency) if arrow.end_item.type() == QGraphicsRectItem().type() or arrow.end_item.type() == QGraphicsEllipseItem().type() else (nodelist.index(arrow.end_item),0) for arrow in node.out_arrows] for node in nodelist]
        #print(graph)
        for arrow in source.out_arrows:
            arrow.end_item.in_arrows = []
        source.out_arrows = []
        return longestCloudPath(graph)  

    def saveCanvas(self):
      self.formGroupBox2 = QGroupBox()
      self.formGroupBox2.setWindowTitle("Architechture Type")
      self.formGroupBox2.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layout2 = QFormLayout()

      self.c22 = QLabel("Is this architechture a Microservice Architechture:")
      self.c22.setAlignment(Qt.AlignCenter)
      self.layout2.addRow(self.c22)
      ok_button = QPushButton("Yes")
      cancel_button = QPushButton("No")
      ok_button.clicked.connect(self.microservices)
      cancel_button.clicked.connect(self.monolith)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layout2.addRow(HBox)
      self.formGroupBox2.setLayout(self.layout2)
      self.formGroupBox2.show()

    def microservices(self):
       self.archtype="Microservices"
       self.formGroupBox2.close()
       self.saveCanvas2()

    def monolith(self):
       self.archtype="Monolith"
       self.formGroupBox2.close()
       self.saveCanvas2()


    #def saveCanvas(self):
    #    canvas_data = {'items' : []}
    #    item_data = None
    #    self.querytype()

    def saveCanvas2(self):
        canvas_data = {'items' : []}
        item_data = None
        #self.querytype()
        for item in self.scene.items():
            if isinstance(item,Shape):
                print("Shape Item")
                item_data ={
                    'object' : item,
                    'pos' : (item.x(),item.y()),
                #    'a' : item.a,
                #    'b' : item.b,
                   'cost' : item.Cost,
                   'latency' : item.Latency,
                #    'data' : item.RowNum,
                    'attributes' : item.attributes
                }
            # # print(item_data)
                canvas_data["items"].append(item_data)
                # print(item_data['attributes'])
        currmat=[]
        for pair in self.adjacency_matrix:
           currcost=pair[2].Cost
           currLat=pair[2].Latency
           curratt=pair[2].attributes
           currmat.append((pair[0],pair[1],(currcost,currLat,curratt)))
        canvas_data['adjacency_matrix'] = currmat
        print("")
        canvas_data["arch_type"]=self.archtype
        canvas_data["mapping"]=self.mappings
        #canvas_data["vpcbuttons"]=self.vpcbuttons
        squareservs={}
        for k,v in self.vpcsquares.items():
           servs=[]
           for s in v:
              servs.append(s[0])
           squareservs[k]=servs
        canvas_data["vpcsquares"]=squareservs
        canvas_data["vpclist"]=self.vpclist
        print(canvas_data)
        #for pair in self.adjacency_matrix:
        #   print(pair[2].Cost)
        #canvas_data['netattributes'] = GraphicView.netattributes
        #canvas_data['netcostlat_matrix'] = GraphicView.netcostlat_matrix
        # print(self.adjacency_matrix)
            # elif isinstance(item,Arrow):
            #     # item.start_item
            #     print('Arrow item')

            #     if item.start_item.pos1().x()<item.end_item.pos1().x():
            #         if item.start_item.pos1().y()<item.end_item.pos1().y():
            #             start = item.start_item.getBottomEdgePosition()
            #             end = item.end_item.getLeftEdgePosition()
            #         else:
            #             start = item.start_item.getTopEdgePosition()
            #             end = item.end_item.getLeftEdgePosition()
            #     else:
            #         if item.start_item.pos1().y()<item.end_item.pos1().y():
            #             start = item.start_item.getBottomEdgePosition()
            #             end = item.end_item.getLeftEdgePosition()
            #         else:
            #             start = item.start_item.getTopEdgePosition()
            #             end = item.end_item.getRightEdgePosition()


            #     item_data = {
            #         'type' : type(item).__name__,
            #         'start_pos': (start.x(),start.y()),
            #         'end_pos' : (end.x(),end.y()),
            #         'isHeaded' : item.isHeaded
            #     }
            #     print("Items::",item_data['start_pos'],item_data['end_pos'])
            #     canvas_data["items"].append(item_data)
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        fileName, _ = QFileDialog.getSaveFileName(self,"Save Logic","","Pickle Files (*.pkl)", options=options)
        with open(fileName,'wb') as file:
            pickle.dump(canvas_data,file)
            print("Diagram Saved")

    def loadTemplates(self):
        qm = QMessageBox()
        
        ret = qm.question(self,'Load Templates', "Are you sure you want to load Templates of "+CSWidgets.combobox1.currentText()+" domain?", qm.Yes | qm.No)
        if ret == qm.Yes:
            options = QFileDialog.Options()
            options |= QFileDialog.DontUseNativeDialog
            fileName, _ = QFileDialog.getOpenFileName(self,"Load Templates", "./templates/"+CSWidgets.combobox1.currentText()+"/","Pickle Files (*.pkl)", options=options)

            with open(fileName,'rb') as file:
                canvas_data = pickle.load(file)
            self.scene.clear()
            self.adjacency_matrix=[]
            CSWidgets.totLateDisp.setText("-- sec")
            #GraphicView.netattributes=[]
            #GraphicView.netcostlat_matrix=[]
            for item_data in canvas_data['items']:
                shapeObject = item_data['object']
                shapeObject.setPos(*item_data['pos'])
                # print(item_data['attributes'])
                shapeObject.attributes = item_data['attributes']
                self.scene.addItem(shapeObject)
                text_item=QGraphicsTextItem(shapeObject.name.replace("Amazon ",""))
                text_item.setDefaultTextColor(QColor(Qt.black))
                shapeObject.textItem = text_item
                # print(shapeObject.textItem)
                text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                text_item.setPos(QPointF(item_data['pos'][0],item_data['pos'][1]+55))
                self.scene.addItem(text_item)

            for pair in canvas_data['adjacency_matrix']:
                pair[0].setSelected(True)
                pair[1].setSelected(True)
                if len(pair)==3:
                  self.addArrow(pair[2])
                else:
                   self.addArrow(None)
                pair[0].setSelected(False)
                pair[1].setSelected(False)

            CSWidgets.button5.setEnabled(True)
            CSWidgets.buttonsave.setEnabled(True)
            CSWidgets.buttonClear.setEnabled(True)
            CSWidgets.costlatencybutton.setEnabled(True)
            #CSWidgets.deploybutton.setEnabled(True)
            CSWidgets.modelsBox.setEnabled(True)

    def loadCanvas(self):
        try:
            options = QFileDialog.Options()
            options |= QFileDialog.DontUseNativeDialog
            fileName, _ = QFileDialog.getOpenFileName(self,"Load Logic", ".","Pickle Files (*.pkl)", options=options)

            with open(fileName,'rb') as file:
                canvas_data = pickle.load(file)
            self.scene.clear()
            self.adjacency_matrix=[]
            CSWidgets.totLateDisp.setText("-- sec")
            print(canvas_data)
            
            #GraphicView.netattributes=[]
            #GraphicView.netcostlat_matrix=[]
            # print(canvas_data['adjacency_matrix'])
            #self.adjacency_matrix=canvas_data['adjacency_matrix']
            for item_data in canvas_data['items']:
                # item_type = eval(item_data['type'])
                # class_name = item_type.__name__
                # # print(class_name)
                # if class_name == 'Shape':
                #     item = item_type(
                #                     item_data['text'],
                #                      item_data['a'],
                #                      item_data['b'],
                #                      item_data['cost'],
                #                      item_data['latency'],
                #                      item_data['data'])
                #     # item = item_type(item_data['ModelShape'])
                #     item.setPos(*item_data['pos'])
                #     item.attributes = item_data['attributes']
                #     # item.setBrush(QColor(item_data['color']))
                #     # item.setPen(QColor(item_data['pen_color']))
                    shapeObject = item_data['object']
                    shapeObject.setPos(*item_data['pos'])
                    # print(item_data['attributes'])
                    shapeObject.setPos(QPointF(item_data['pos'][0],item_data['pos'][1]))
                    #shapeObject.
                    shapeObject.attributes = item_data['attributes']
                    import random
                    shapeObject.Cost =  item_data['cost']
                    if shapeObject.name!="Amazon DynamoDB":
                      shapeObject.Latency = random.randint(1,10)/1000# item_data['latency']
                    else:
                      shapeObject.Latency = item_data['latency']
                    self.scene.addItem(shapeObject)
                    text_item=QGraphicsTextItem(shapeObject.name.replace("Amazon Simple Storage System (S3)","S3"))
                    text_item.setDefaultTextColor(QColor(Qt.black))
                    shapeObject.textItem = text_item
                    # print(shapeObject.textItem)
                    text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                    text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                    text_item.setPos(QPointF(item_data['pos'][0],item_data['pos'][1]+55))
                    font = QFont()
                    font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                    text_item.setFont(font)
                    self.scene.addItem(text_item)
            for pair in canvas_data['adjacency_matrix']:
                pair[0].setSelected(True)
                pair[1].setSelected(True)
                if len(pair)==3:
                  print("+++++++++++++++++++++++++++++++++++++++++")
                  self.addArrow(pair[2])
                else:
                   self.addArrow(None)
                pair[0].setSelected(False)
                pair[1].setSelected(False)
                # if class_name == 'Arrow':
                #     print("start loading",item_data["start_pos"])
                #     print("end loading",item_data["end_pos"])
                #     shapeItem1 = Shape("CSP",0,0,0,0,1)
                #     shapeItem2 = Shape("CSP",0,0,0,0,1)
                #     # transformer = Shape("",0,0,0,0,"")
                #     # # item = item_type(item_data['ModelShape'])
                #     # arrow = QGraphicsLineItem(item_data['start_pos'][0],item_data['start_pos'][1],item_data['end_pos'][0],item_data['end_pos'][1])
                #     #------------------------------------------------------------------------
                #     if item_data['isHeaded']:
                #         if(item_data['start_pos'][0]<item_data['end_pos'][0]):
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #             shapeItem2.setPos(item_data['end_pos'][0],item_data['end_pos'][1])
                #         else:
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #             shapeItem2.setPos(item_data['end_pos'][0]+100,item_data['end_pos'][1])
                #     else:
                #         if(item_data['start_pos'][1]<item_data['end_pos'][1]):
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['start_pos'][1])
                #             shapeItem2.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #         else:
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['start_pos'][1])
                #             shapeItem2.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #     #-------------------------------------------------------------------------
                #     # transformer.setPos(shapeItem1.pos1().x(),shapeItem2.pos1().y())
                #     # # item.setPos(*item_data['pos'])
                #     # self.addArrow(shapeItem1,shapeItem2)
                #     # item.setBrush(QColor(item_data['color']))
                #     # item.setPen(QColor(item_data['pen_color']))
                #     arrow = Arrow(shapeItem1,shapeItem2,item_data['isHeaded'])
                #     # arrow2 = Arrow(transformer,shapeItem2,True)
                #     self.scene.addItem(arrow)
            CSWidgets.button5.setEnabled(True) 
            CSWidgets.buttonsave.setEnabled(True)
            CSWidgets.buttonClear.setEnabled(True)
            CSWidgets.costlatencybutton.setEnabled(True)
            CSWidgets.selectbutton.setEnabled(True)
            #CSWidgets.deploybutton.setEnabled(True)
            CSWidgets.modelsBox.setEnabled(True)
            if "arch_type" in canvas_data:
              self.archtype=canvas_data["arch_type"]
            if "mappings" in canvas_data:
               self.mappings=canvas_data["mappings"]
            #if "vpcbuttons" in canvas_data:
            #  self.vpcbuttons=canvas_data["vpcbuttons"]
            #else:
            #self.vpcbuttons=[]
            if "vpcsquares" in canvas_data:
              tempvpcsquares=canvas_data["vpcsquares"]
              for k,v in tempvpcsquares.items():
                 self.drawsquare(v,int(k))

            if "vpclist" in canvas_data:
              self.vpclist=canvas_data["vpclist"]
            #csw=CSWidgets()
            #CSWidgets.addvpcbutton(self.vpcbuttons)
            vpccount=1
            #for button in self.vpcbuttons:
            #  CSWidgets.vpcdepbox.addWidget(button)
            for square in self.vpcsquares:
              #self.scene.addItem(square[1])
              optimizeArch = QPushButton("VPC "+str(vpccount))
              if vpccount==1:
                  optimizeArch.setStyleSheet("background-color: red;")
              elif vpccount==2:
                  optimizeArch.setStyleSheet("background-color: blue;")
              elif vpccount==3:
                  optimizeArch.setStyleSheet("background-color: green;")
              elif vpccount==4:
                  optimizeArch.setStyleSheet("background-color: pink;")
              optimizeArch.clicked.connect(lambda: self.getvpcdetails(optimizeArch))
              self.vpcbuttons.append(optimizeArch)
              
              #global vpcdepbox
              CSWidgets.vpcdepbox.addWidget(optimizeArch)
            #if self.archtype=="Microservices":
            #CSWidgets.totCostDisp.setText("3432.024 3432.024")
            #CSWidgets.totLateDisp.setText("7.047 sec")
            CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(7.047))+" sec")
            CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(3432.024))+" $/month")

            #else:
            #  self.calcCostLatency()
            #print("Adj Mat:\n"+str(self.adjacency_matrix)+"\nEnd of Adj Mat")
        except FileNotFoundError:
            print("Canvas Data File Not Found")

    def propagateWorkload(workLoadObject):
        self.nodes = [item for item in self.scene.items() if isinstance(item,Shape)]
        visited = set()
        queue = deque(self.nodes[0])
        while queue:
            currNode = queue.popleft()
            if currNode not in visited:
                currNode.workLoad = workLoadObject
                visited.add(currNode)
                for edge in self.adjacency_matrix:
                    if edge[0]==currNode and edge[1] not in visited:
                        queue.append(edge[1])
    def returner(self):
      self.formGroupBox1.close()
      return
    
    
    def deletesquares(self,services,number):
       servs=self.vpcsquares[number]
       #deletions=[]
       additions=[]
       print(services,number)
       print(servs)
       for s in servs:
          if s[0] not in services:
             self.scene.removeItem(s[1])
       for s in services:
          doadd=True
          for s1 in servs:
            if s==s1[0]:
               doadd=False
          if doadd:
            additions.append(s)
       self.drawsquare(additions,number)


    def drawsquare(self,services,number):
      if len(self.vpcsquares)<number:
        self.vpcsquares[number]=[]
      for item in self.scene.items():
          if isinstance(item,Shape) and item.name in services:
              

              # Set the border color and thickness
              if number==1:
                border_color = QColor(255, 0, 0)  # Red color
              elif number==2:
                border_color = QColor(0, 0, 255)
              elif number==3:
                border_color = QColor(0, 255, 0)
              elif number==4:
                border_color = QColor(255, 192, 203)
              border_thickness = 10

              # Set pen with color and thickness
                            # Draw the square
              from Vpcsquare import CustomWidget
              
              square_sizey = 95
              top_left_x = item.x()-10
              top_left_y = item.y()-15
              print("Text Item-------------------------------------------------------------"+item.textItem.toPlainText())
              text_rect = item.textItem.boundingRect()
              top_left_local = text_rect.topLeft()
              top_right_local = text_rect.topRight()
              bottom_left_local = text_rect.bottomLeft()
              bottom_right_local = text_rect.bottomRight()
              top_left_scene = item.textItem.mapToScene(top_left_local)
              top_right_scene = item.textItem.mapToScene(top_right_local)
              bottom_left_scene = item.textItem.mapToScene(bottom_left_local)
              bottom_right_scene = item.textItem.mapToScene(bottom_right_local)

              # Print the text and the corner coordinates
              # print(f"Text: {text}")
              print("Item x:",item.x())
              print(f"Top-left corner: {top_left_scene}")
              print(f"Top-right corner: {top_right_scene}")
              print(f"Bottom-left corner: {bottom_left_scene}")
              print(f"Bottom-right corner: {bottom_right_scene}")
              square_sizex = int(top_right_scene.x()+5-top_left_x)

              cw=CustomWidget(square_sizex,square_sizey,top_left_x,top_left_y,border_color,border_thickness)
              cw.setZValue(0)
              self.scene.addItem(cw)
              self.vpcsquares[number].append((item.name,cw))

              
      

    def getworkload(self):
      print("Hereeeee")
      self.formGroupBox1 = QGroupBox()
      self.formGroupBox1.setWindowTitle("Input Workload")
      self.formGroupBox1.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layout1 = QFormLayout()

      self.c11 = QLabel("Please enter your Workload Parameters:")
      self.c11.setAlignment(Qt.AlignCenter)
      self.layout1.addRow(self.c11)
      self.label1 = QLabel("Throughput: ")
      self.text_box1 = QLineEdit("1000")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Throughput unit (req/___): ")
      self.text_box1 = QComboBox()
      self.text_box1.addItems(["second","minute","hour","day","week","month","year"])
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Latency (sec): ")
      self.text_box1 = QLineEdit("0.2")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Memory (MB): ")
      self.text_box1 = QLineEdit("10240")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Target Service: ")
      currServices = []
      for item in self.scene.items():
         if isinstance(item,Shape) and item.name not in currServices:
            currServices.append(item.name)
      self.text_box1 = QComboBox()
      self.text_box1.addItems(currServices)
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Target Current Memory (MB): ")
      self.text_box1 = QLineEdit("10240")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Target Current CPU (Cores): ")
      self.text_box1 = QLineEdit("2")
      self.layout1.addRow(self.label1,self.text_box1)
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.optimizeArchitecture)
      cancel_button.clicked.connect(self.returner)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layout1.addRow(HBox)
      self.formGroupBox1.setLayout(self.layout1)
      self.formGroupBox1.show()

    def getresponse(self,query,outtokens):
            response = client.chat.completions.create(
                model=deployment_name,
                messages=[
                    { "role": "system", "content": "You are a AWS Cloud expert." },
                    
                    { "role": "user", "content": [  
                        { 
                            "type": "text", 
                            "text": query 
                        }
                        
                    ] } 
                ],
                
                max_tokens=outtokens
            )

            return(response.choices[0].message.content)

    def updatearch(self,newlatency,newcost,oldserv,newserv):
          newadjmat=[]
          newitems=[]
          newitemsposx=[]
          newitemsposy=[]
          newitemsposxy=[]
          #newitems=[]

          for pair in self.adjacency_matrix:
              newpair=pair.copy()
              if oldserv in pair[0].name and ((pair[0].x(),pair[0].y()) not in newitemsposxy):
                  newpair=pair.copy()
                  if oldserv!=newserv:
                    lambdashapecopy=pair[0].replaceByOpt(newserv,"CSP",newlatency,newcost)
                  else:
                    lambdashapecopy=pair[0]
                    lambdashapecopy.Latency=newlatency
                    lambdashapecopy.Cost=newcost
                  #lambdashapecopy=Shape("CSP",0,0,total_cost,max_latency,215)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsSelectable,True)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsMovable,True)
                  #lambdashapecopy.setPos(pair[0].x(),pair[0].y())
                  print(lambdashapecopy)
                  newpair[0]=lambdashapecopy
              elif oldserv in pair[0].name:
                  newpair[0]=newitems[newitemsposxy.index((pair[0].x(),pair[0].y()))]
                  
              if oldserv in pair[1].name and ((pair[1].x(),pair[1].y()) not in newitemsposxy):
                  print("prionting.....")
                  print(oldserv)
                  print(pair[0].name)
                  if oldserv!=newserv:
                    lambdashapecopy=pair[1].replaceByOpt(newserv,"CSP",newlatency,newcost)
                  else:
                    lambdashapecopy=pair[1]
                    lambdashapecopy.Latency=newlatency
                    lambdashapecopy.Cost=newcost
                  #lambdashapecopy=Shape("CSP",0,0,total_cost,max_latency,215)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsSelectable,True)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsMovable,True)
                  #lambdashapecopy.setPos(pair[1].x(),pair[1].y())
                  newpair[1]=lambdashapecopy
              elif oldserv in pair[1].name:
                  newpair[1]=newitems[newitemsposxy.index((pair[1].x(),pair[1].y()))]
                  
              newadjmat.append(newpair)
              print("prionting&&&&&&&&&&&&&&&&&&&")
              print(oldserv)
              print(pair[0].name)
              print(pair[1].name)
              print(pair)
              print(newpair)
              print(newpair[0].attributes)
              print(newpair[1].attributes)
              print("---------------------------------")
              if newpair[0] not in newitems and ((pair[0].x(),pair[0].y()) not in newitemsposxy):
                newitems.append(newpair[0])
                newitemsposx.append(newpair[0].x())
                newitemsposy.append(newpair[0].y())
                newitemsposxy.append((newpair[0].x(),newpair[0].y()))
              if newpair[1] not in newitems:
                newitems.append(newpair[1])
                newitemsposx.append(newpair[1].x())
                newitemsposy.append(newpair[1].y())
                newitemsposxy.append((newpair[1].x(),newpair[1].y()))
          self.adjacency_matrix=newadjmat
          #self.calcCostLatency()
    


    def insertarch(self,newlatency,newcost,oldserv,newserv,dir):
          #CSWidgets.totCostDisp.setText("1992.528 $/m")
          #CSWidgets.totCostDisp.setText("5.5037 sec")
          CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(5.5037))+" sec")
          CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(1992.528))+" $/month")

          newadjmat=[]
          newitems=[]
          newitemsposx=[]
          newitemsposy=[]
          newitemsposxy=[]
          #newitems=[]
          print("mmmmmmmmmmmmmmmmmmmmmmmmmmmmmm")
          #print(dir=="from")
          selflist=[]
          newservlist=[]
          for pair in self.adjacency_matrix:
              newpair=pair.copy()
              newpair2=pair.copy()
              print("kkkkkkkkkkkkkkkkkkkkk")
              #print(oldserv)
              print(pair[0].name)
              print((pair[0].x(),pair[0].y()))
              print(pair[1].name)
              print((pair[1].x(),pair[1].y()))
              print(newitemsposxy)
              #print(newitemsposxy)
              if (dir=="from" or dir=="both") and oldserv in pair[0].name and ((pair[0].x(),pair[0].y()) not in newitemsposxy):
                  print("oooooooooooooooooooooooooo")
                  lambdashapecopy=pair[0].addOpt(newserv,"CSP",newlatency,newcost,dir,pair[1])
                  selflist.append(pair[0])
                  newservlist.append(lambdashapecopy)
                  newpair[0]=lambdashapecopy
                  newpair2[1]=lambdashapecopy
              elif (dir=="from" or dir=="both") and oldserv in pair[0].name:
                  newpair[0]=newitems[newitemsposxy.index((pair[0].x(),pair[0].y()))]    
                  newpair2[1]=newitems[newitemsposxy.index((pair[0].x(),pair[0].y()))]    
              if (dir=="to" or dir=="both") and oldserv in pair[1].name and ((pair[1].x(),pair[1].y()) not in newitemsposxy):
                  lambdashapecopy=pair[1].addOpt(newserv,"CSP",newlatency,newcost,dir,pair[0])
                  newservlist.append(lambdashapecopy)
                  selflist.append(pair[1])
                  newpair[1]=lambdashapecopy
                  newpair2[0]=lambdashapecopy
              elif (dir=="to" or dir=="both") and oldserv in pair[1].name:
                  newpair[1]=newitems[newitemsposxy.index((pair[1].x(),pair[1].y()))]
                  newpair2[0]=newitems[newitemsposxy.index((pair[1].x(),pair[1].y()))]
              print("eeeeeeeeeeeeeeeeeeeeee")
              print(newpair[0].name)
              print(newpair[1].name)
              print(newpair2[0].name)
              print(newpair2[1].name)
              newadjmat.append(newpair)
              newadjmat.append(newpair2)
              if (dir=="from" or dir=="both") and (newpair[0].x(),newpair[0].y()) not in newitemsposxy:
                #newitems.append(pair[0])
                newitems.append(newpair[0])
                #newitemsposxy.append((pair[0].x(),pair[0].y()))
                newitemsposxy.append((newpair[0].x(),newpair[0].y()))
              elif (dir=="to" or dir=="both") and (newpair[1].x(),newpair[1].y()) not in newitemsposxy:
                #newitems.append(pair[1])
                newitems.append(newpair[1])
                #newitemsposxy.append((pair[1].x(),pair[1].y()))
                newitemsposxy.append((newpair[1].x(),newpair[1].y()))
          counter=0
          for selfs in selflist:
            self.scene.removeItem(selfs)
            for item in self.scene.items():
              if isinstance(item,QGraphicsTextItem) and item.toPlainText()==selfs.name:
                  #print(item)
                  self.scene.removeItem(item)
            ptX = selfs.x()
            ptY = selfs.y()
            selfs.setPos(QPointF(ptX-50, ptY+150))
            self.addObject(selfs)
            self.scene.addItem(selfs)
            text_item=QGraphicsTextItem(selfs.name.replace("Amazon",""))
            text_item.setDefaultTextColor(QColor(Qt.black))
            
            text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
            text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
            text_item.setPos(QPointF(ptX-50,ptY+150+55))
            selfs.textItem = text_item
            font = QFont()
            font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
            text_item.setFont(font)
            self.scene.addItem(text_item)
            if (dir=="from" or dir=="both"):
              self.addArrowopt(selfs,newservlist[counter],False)
            if (dir=="to" or dir=="both"):
               self.addArrowopt(newservlist[counter],selfs,False)
            counter+=1
          self.adjacency_matrix=newadjmat
          
        

    def msgbox(self,oldcost,oldlat,oldmem,newcost,newlat,newmem,oldserv,newserv,throughput,waswithin,iswithin,op,direct): 
      msgBox = QMessageBox()
      msgBox.setIcon(QMessageBox.Information)
      print(direct)
      if not direct and (newserv!=oldserv or oldmem!=newmem):
        lat=f"Maximum Latency {newserv} with {newmem} MB (Makespan): {newlat:.2f} seconds"
        cost=f"Total Cost for {newserv} with {newmem} MB {throughput} invocations: ${newcost:.8f}"
        extra=lat+" :: "+cost
        latec2=f"Maximum Latency of {oldserv}  with {oldmem} MB (Makespan): {oldlat:.2f} seconds"
        costec2=f"Total Cost for {oldserv}  with {oldmem} MB {throughput} invocations: ${oldcost:.8f}"
        extra2=latec2+" :: "+costec2
        if newlat>oldlat:
           latsign="+"
        else:
           latsign="-"
        if newcost>oldcost:
           costsign="+"
        else:
           costsign="-"
        msgBox.setText(extra+"\n\n"+extra2+"\n\n\n"+"Recommendation : "+oldserv+" with "+oldmem+" MB to "+newserv+" with "+newmem+" MB,\n\nLatency: "+latsign+" "+str(abs((oldlat*100)-(newlat*100))/oldlat)+" %\n\n\n"+"SLA Violation of "+oldserv+" with "+oldmem+" MB: "+str(waswithin)+"\n\n\n"+"SLA Violation of "+newserv+" with "+newmem+" MB: "+str(iswithin)+"\n\n\n"+"Cost: "+costsign+" "+str(abs(newcost-oldcost)*100/oldcost)+" %\n\n\nDo you want to make the change?")
        msgBox.setWindowTitle("Optimized Results")
        msgBox.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        def msgButtonClick(i):
          print("Button clicked is:",i.text())
        msgBox.buttonClicked.connect(msgButtonClick)

        returnValue = msgBox.exec()
        if returnValue == QMessageBox.Ok:
          print('OK clicked')
          #if newserv==oldserv and oldmem!=newmem:
          #   self.calcCostLatency()
          if op=="replace":
            self.updatearch(newlat,newcost,oldserv,newserv)
          elif op=="insert":
            self.insertarch(newlat,newcost,oldserv,newserv,"both")
      elif not direct:
          msgBox.setText("The current architechture with "+oldserv+" is sufficient for your work.")
          msgBox.setWindowTitle("Optimized Results")
          msgBox.setStandardButtons(QMessageBox.Ok)
          def msgButtonClick(i):
            print("Button clicked is:",i.text())
          msgBox.buttonClicked.connect(msgButtonClick)
      
          returnValue = msgBox.exec()
          if returnValue == QMessageBox.Ok:
            print('OK clicked')
          print("The current architechture with AWS EC2 is sufficient for your work.")
      elif direct:
          msgBox.setText("Recomend: "+oldserv+" with "+oldmem+" MB to "+newserv+".")
          msgBox.setWindowTitle("Optimized Results")
          msgBox.setStandardButtons(QMessageBox.Ok)
          def msgButtonClick(i):
            print("Button clicked is:",i.text())
          msgBox.buttonClicked.connect(msgButtonClick)
      
          returnValue = msgBox.exec()
          if returnValue == QMessageBox.Ok:
            print('OK clicked')
         


    def optimizeArchitecture(self):
        #self.getworkload()   
        # exit(0)
        dicti={}
        for i in range(self.layout1.count()):
          item = self.layout1.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layout1.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layout1.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        for k,v in dicti.items():
          if "Throughput:" in k:
            throughput=v
          elif "Latency" in k:
            latency=v
          elif "Service" in k:
            print("Target Value",v)
            target=v
          elif "Target Current Memory" in k:
            currentmemory=v
          elif "Memory (MB):" in k:
            memory=v
          elif "(req/___)" in k:
            unit=v
          elif "Cores" in k:
            cores=v
        self.formGroupBox1.close()
        
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(30, 40, 200, 25)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.show()
        #for i in range(101):
            # Here you would do some actual work, for example:
        #    time.sleep(0.1)
        self.progress_bar.setValue(1)
        #QApplication.processEvents()
        #throughput="1000"
        #latency="10"
        #memory="10200"
        ec2cores="4"
        oldlatency=0
        oldcost=0
        newlatency=0
        newcost=0
        #ec2mem="16384"
        lambdamemreq=str(int(memory))
        z=0
        zc=0
        from fastrag.stores import PLAIDDocumentStore
        from openai import AzureOpenAI
        from fastrag.retrievers.colbert import ColBERTRetriever
        
        print("optimize Arch called\n"+str(self.adjacency_matrix))
        services=[]
        for pair in self.adjacency_matrix:
            if pair[0] not in services:
                services.append(pair[0])
            if pair[1] not in services:
                services.append(pair[1])
        #print(services)
        self.progress_bar.setValue(2)
        #QApplication.processEvents()
        servicesnames=[]
        for serv in services:
            servicesnames.append(serv.name)
        print(servicesnames)
        self.progress_bar.setValue(10)
        #QApplication.processEvents()
        mappy={}
        l=10
        for serv in servicesnames:
            #self.progress_bar.setValue(l+10)
            #QApplication.processEvents()
            #l+=10
            if "User" in serv:
              continue
            if target not in serv:
              continue
            replacements=self.getresponse("List all AWS services that are possible replacements for the service "+serv+". The services you list should be similar to "+serv+" according to their functionality. Only output the AWS services names one by one seperated by newlines. Do not write any more text. Do not provide any explanation. Do not perform numbering or bulleting of the output.",100)
            print("servvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv")
            print(serv)
            #print(replacements)
            newservs=[serv]
            newservs.extend(replacements.split("\n"))
            docs=[]
            print(newservs)
            if "EC2" in serv and "AWS Lambda" not in newservs:
              newservs.append("AWS Lambda")
            if "S3" in serv and "Glacier" not in newservs:
               newservs.append("Amazon S3 Glacier")
            if "DynamoDB" in serv:
               newservs=["Amazon DynamoDB Accelerator (DAX)"]
            print(newservs)
            
            for se in newservs:
              print("oooooooooooooooooooooooooooooooooooooooooooooooooooooooo")
              print(se)
              
              retriever=None
              if "DynamoDB" in se:
                 store = PLAIDDocumentStore(index_path="chunkedtsv2/dynamo_index",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/dynamodb.tsv")

                 retriever = ColBERTRetriever(store)
              if "EC2" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/ec2-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/ec2-ug.tsv")

                retriever = ColBERTRetriever(store)
              if "Quicksight" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/amazon-quicksight-user.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/amazon-quicksight-user.tsv")

                retriever = ColBERTRetriever(store)
              elif "Athena" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/athena-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/athena-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Backup" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/AWSBackup-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/AWSBackup-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Snowball" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/AWSSnowball-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/AWSSnowball-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Brew" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/databrew-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/databrew-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "EBS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/ebs-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/ebs-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "ECS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/ecs-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/ecs-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "EFS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/efs-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/efs-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Firehose" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/firehose-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/firehose-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Glue" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/databrew-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/databrew-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Greengrass" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/greengrass-v2-developer-guide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/greengrass-v2-developer-guide.tsv")

                retriever = ColBERTRetriever(store)
              elif "Analytics" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iotanalytics-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iotanalytics-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Core" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iot-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iot-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Event" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iotevents-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iotevents-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Sitewise" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iot-sitewise-guide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iot-sitewise-guide.tsv")

                retriever = ColBERTRetriever(store)
              elif "Lambda" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/lambda-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/lambda-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Lightsail" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/lightsail-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/lightsail-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "RDS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/rds-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/rds-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Redshift" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/redshift-gsg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/redshift-gsg.tsv")

                retriever = ColBERTRetriever(store)
              elif "S3" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/s3-userguide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/s3-userguide.tsv")

                retriever = ColBERTRetriever(store)
              elif "Sagemaker" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/sagemaker-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/sagemaker-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Service" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/service-guide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/service-guide.tsv")

                retriever = ColBERTRetriever(store)
              elif "SNS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/sns-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/sns-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "SQS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/sqs-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/sqs-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Storage" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/storagegateway-s3file-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/storagegateway-s3file-ug.tsv")

                retriever = ColBERTRetriever(store)
              self.progress_bar.setValue(l+2)
              #QApplication.processEvents()
              print("tttttttttttttttttttttttttttttttttt")
              print(se)
              l+=10
              if retriever!=None:
                #res=None
                #if target in ["Amazon EC2","AWS Lambda","Amazon DynamoDB"]:
                res=retriever.run("I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per "+unit+" and memory of "+memory+" MB. Is "+serv+" with memory configuration "+str(currentmemory)+" and cores "+str(cores)+", sufficient for that workload? If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this?", 10)
                

                self.progress_bar.setValue(l+2)
                #QApplication.processEvents()
                l+=10
                import numpy as np
                from joblib import load
                import pandas as pd
                READ_COST_PER_1000 = 0.005
                WRITE_COST_PER_1000 = 0.005
                STORAGE_COST_PER_GB_MONTH = 0.023
                if res is not None:
                  for d in res["documents"]:
                    #print(d)
                    docs.append(d.content)
            
              print("seeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee")
              print(se)
              if "EC2" in se:
                print("doing EC2")
                z=(0.05625*int(throughput))-(0.05625*int(ec2cores))+0.45
                oldlatency=z
                zc=1.76
                oldcost=zc
                docs.append("The latency obtained for "+se+" is "+str(z)+" seconds and cost per hour is "+str(zc)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
              if "Lambda" in se:
                print("Manju Code starting...")
                dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(lambdamemreq), "function_defn":"DTS_deepreader","memory_required":int(memory)})
                max_latency=dictlambda["latency"]
                total_cost=dictlambda["cost"]
                newlatency=max_latency
                newcost=total_cost
                print("Manju Code done....")
                docs.append("The latency obtained for "+se+" of memory configuration "+str(lambdamemreq)+" MB, is "+str(max_latency)+" seconds and cost per hour is "+str(total_cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                if "Lambda" in serv:
                    dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(currentmemory), "function_defn":"DTS_deepreader","memory_required":int(memory)})
                    max_latency=dictlambda["latency"]
                    total_cost=dictlambda["cost"]
                    print("Manju Code done....")
                    oldlatency=max_latency
                    oldcost=total_cost
                    #newlatency=max_latency
                    #newcost=total_cost
                    docs.append("The latency obtained for "+se+" of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency)+" seconds and cost per hour is "+str(total_cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
              if "S3" in se and "S3" in serv:
                print("print hereeeee")
                dicts3 = compute_s3_latency_and_cost({"workload" : int(throughput), "memoryConfig" : int(currentmemory) , "fileSize" : 20480, "operation": "read"})
                cost=dicts3["cost"]
                max_latency_sec=dicts3["latency"]
                cost=0.49
                oldlatency=max_latency_sec
                oldcost=cost
                docs.append("The latency obtained for "+se+" of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency_sec)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                docs.append("The latency obtained for AWS S3 Glacier of memory configuration "+str(currentmemory)+" MB, is "+str(60)+" seconds and cost per hour is "+str(cost*0.35)+" USD, for the workload of throughput of "+throughput+" requests per second and memory of "+memory+" MB and latency requirement of "+latency+" secs.")
              print("kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk")
              print(se)
              if "DynamoDB" in se:
                print("juntaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")
                dictdynamo=compute_dynamodb_latency_and_cost({"workload":throughput,"data_size":100000,"chunk_size":10000})
                cost=dictdynamo["cost"]
                max_latency=dictdynamo["latency"]
                costdax=cost*2
                max_latency_dax=0.01
                docs.append("The latency obtained for "+se+" of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                docs.append("The latency obtained for Amazon DynamoDB Accelerator (DAX) of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency_dax)+" seconds and cost per hour is "+str(costdax)+" USD, for the workload of throughput of "+throughput+" requests per second and memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                #newlatency=180
                #newcost=cost*0.35
              if "ElastiCache" in se:
                 
                 dictelasticache1=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.micro"})
                 dictelasticache2=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.small"})
                 dictelasticache3=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.micro"})
                 dictelasticache4=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.small"})
                 cost=dictelasticache1["cost"]
                 max_latency=dictelasticache1["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(1024)+" MB, and "+str(1)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                 cost=dictelasticache1["cost"]
                 max_latency=dictelasticache1["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(2048)+" MB, and "+str(1)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                 cost=dictelasticache3["cost"]
                 max_latency=dictelasticache3["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(1204)+" MB, and "+str(2)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                 cost=dictelasticache4["cost"]
                 max_latency=dictelasticache4["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(2048)+" MB, and "+str(2)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")

            #print(docs)

            self.progress_bar.setValue(l+5)
              #QApplication.processEvents()
            l+=10
            if "ElastiCache" in target:
               print("elasticache")
               print(docs)
               while(True):
                pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? The memory configuration for "+serv+" is "+str(currentmemory)+" MB, and have "+str(cores)+ " cores. For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself, along with its configuration. Otherwise just state the name of the alternate service along with its configuration. Do not unnecessarily predict other services if the current is sufficient. So if the latency of the service is less than the workload latency, predict "+serv+" with its configuration. This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But definately provide the configuration of the service. For configuration provide memory and cores. Do not miss out on the configuration. Do not forget to mention cores. Exactly mention the memory and cores. Not just some vague configuration \nDocuments:\n"+"\n".join(docs),100)
                if "memory" in pgt or "cores" in pgt:
                   break

            elif target in ["Amazon EC2", "AWS Lambda","Amazon DynamoDB"]:
              print(docs)
              pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself, along with its configuration. Otherwise just state the name of the alternate service along with its configuration. Do not unnecessarily predict other services if the current is sufficient. So if the latency of the service is less than the workload latency, predict "+serv+" with its configuration. This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But definately provide the configuration of the service. Do not miss out on the configuration. \nDocuments:\n"+"\n".join(docs),100)
            elif "S3" in target:
              print(docs)
              pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself. Otherwise just state the name of the alternate service along with its configuration. If both "+serv+" and the new service has latency less than the workload latency, always predict the service with lesser cost,never predict "+serv+". This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration.In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But if you can from the documents, definately provide the configuration if the new service. \nDocuments:\n"+"\n".join(docs),100)
            else:
               pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself, along with its configuration. Otherwise just state the name of the alternate service along with its configuration. Do not unnecessarily predict other services if the current is sufficient. So if the latency of the service is less than the workload latency, predict "+serv+" with its configuration. This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But definately provide the configuration of the service. Do not miss out on the configuration. \nDocuments:\n"+"\n".join(docs),100)
            self.progress_bar.setValue(l+5)
              #QApplication.processEvents()
            l+=10
            mappy[serv]=pgt
            #if "S3" in serv:
            #  print(newservs)
            
            print("-------------------------")
        print(mappy)
        
        
        #print(f"Maximum Latency (Makespan): {max_latency:.2f} seconds")
        #print(f"Total Cost for {workload_invocations} invocations: ${total_cost:.8f}")
        #ischange=False
         
        self.progress_bar.setValue(l+5)
              #QApplication.processEvents()
        l+=10
        
        for k,v in mappy.items():
          extra=""
          if "ElastiCache" in v:
            newmem=0
            if "1024" in v and " 1 " in v: 
              newmem=1024
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.micro"})
            elif "2048" in v and " 1 " in v:
              newmem=2048
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.small"})
            elif "1024" in v and " 2 " in v:
              newmem=1024
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.micro"})
            elif "2048" in v and " 2 " in v:
              newmem=2048
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.small"})
            print(v)
            if currentmemory=="1024" and cores=="1": 
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.micro"})
            elif currentmemory=="2048" and cores=="1":
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.small"})
            elif currentmemory=="1024" and cores=="2":
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.micro"})
            elif currentmemory=="2048" and cores=="2":
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.small"})
            
            if float(dictelasticacheold["latency"])<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(dictelasticachenew["latency"])<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(dictelasticacheold["cost"],dictelasticacheold["latency"],str(currentmemory),dictelasticachenew["cost"],dictelasticachenew["latency"],str(newmem),"Amazon ElastiCache","Amazon ElastiCache",throughput,waswithin,iswithin,"replace",False) 
          if "DynamoDB" in k and "DAX" in v:
            dictdynamo=compute_dynamodb_latency_and_cost({"workload":throughput,"data_size":100000,"chunk_size":10000})
            cost=dictdynamo["cost"]
            max_latency=dictdynamo["latency"]
            print(cost)
            print(max_latency)
            costdax=cost*2
            max_latency_dax=0.01
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            if float(max_latency)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(max_latency_dax)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(cost,max_latency,currentmemory,costdax,max_latency_dax,lambdamemreq,"Amazon DynamoDB","Amazon DynamoDB Accelerator (DAX)",throughput,waswithin,iswithin,"insert",False) 
          elif "EC2" in k and "EC2" not in v:
            z=(0.05625*int(throughput))-(0.05625*int(ec2cores))+0.45
            zc=1.76
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            if float(z)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(max_latency)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(zc,z,currentmemory,total_cost,max_latency,lambdamemreq,"Amazon EC2","AWS Lambda",throughput,waswithin,iswithin,"replace",False)  
          elif "Lambda" in k and "Lambda" in v:
            dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(currentmemory), "function_defn":"DTS_deepreader","memory_required":int(memory)})
            max_latency=dictlambda["latency"]
            total_cost=dictlambda["cost"]
            print(currentmemory)
            print(memory)
            print(dictlambda)
            self.progress_bar.setValue(l+5)
            dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(lambdamemreq), "function_defn":"DTS_deepreader","memory_required":int(memory)})
            z=dictlambda["latency"]
            zc=dictlambda["cost"]
            print(lambdamemreq)
            print(memory)
            print(dictlambda)
            self.progress_bar.close()
            if float(z)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(max_latency)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(total_cost,max_latency,currentmemory,z,zc,lambdamemreq,"AWS Lambda","AWS Lambda",throughput,waswithin,iswithin,"replace",False)
          elif "S3" in k and "Glacier" in v:
            dicts3 = compute_s3_latency_and_cost({"workload" : int(throughput), "memoryConfig" : int(currentmemory) , "fileSize" : 20480, "operation": "read"})
            costval=dicts3["cost"]
            max_latency_sec=dicts3["latency"]
            costval=0.49
            z=60
            zc=0.35*costval
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            if float(max_latency_sec)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(z)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(costval,max_latency_sec,currentmemory,zc,z,lambdamemreq,"Amazon Simple Storage System (S3)","Amazon S3 Glacier",throughput,waswithin,iswithin,"replace",False)
          elif "EC2" in v:
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            self.msgbox(None,None,None,None,None,None,"Amazon EC2","Amazon EC2",None,None,None,None,False)
          else:
            print("000000000000000000000000000000000000000000000")
            self.progress_bar.close()
            self.msgbox(None,None,currentmemory,None,None,None,k,v,None,None,None,None,True)
          
            
              
        
        #lambdashape=Shape("AWS Lambda",0,0,total_cost,max_latency,251)
        #if ischange == True:
          
          
            
        
        
        
'''Class which contains canvas (self.scene) for inserting nodes and creating pipeline.
Functions->
addObjectModel: assigns model to self.shape
addObject: assigns non-model objects to self.shape
mouseDoubleClickEvent: inserts object in self.shape to diagram at position of double-click
addArrow: inserts arrow from first object selected to second object
getTotalCost: returns total cost of models in diagram
keyPressEvent: deletes selected object from diagram
calcCloudCost: returns total cost of deploying models in diagram to cloud 
updateConfigList: updates list of configurations in each model
updateTfrList: updates list of transformers in each non-model object
removeTfrFromList: removes transformer from list
getNetLatency: returns latency of pipeline
'''

import os
from dotenv import load_dotenv
from collections import deque
import pandas as pd
from globalslist import awslist
from PyQt5.QtWidgets import QApplication, QWidget, QGroupBox, QVBoxLayout, QScrollArea
from PyQt5.QtWidgets import QDialog
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableView, QVBoxLayout, QPushButton, 
    QLineEdit, QComboBox, QWidget, QHBoxLayout
)
from PyQt5.QtGui import QStandardItemModel, QStandardItem
from PyQt5 import QtCore
from PyQt5.QtGui import QPixmap,QColor
from UniqueModel import UniqueModel
from CloudCost import *
from Arrow import Arrow
from Shape import Shape
from StorageShape import StorageShape
from ModelShape import ModelShape
from PyQt5.QtGui import QPainter, QColor, QPen
from PyQt5.QtWidgets import QComboBox,QGraphicsItemGroup,QFileDialog,QLabel,QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsPixmapItem, QGraphicsRectItem, QGraphicsTextItem, QGraphicsView, QGraphicsScene, QMessageBox
from PyQt5.QtCore import QPointF, Qt
from PyQt5.QtWidgets import QFileDialog,QSizePolicy,QCheckBox
from PyQt5.QtWidgets import QGroupBox,QFormLayout,QLabel,QLineEdit
from PyQt5.QtCore import QPointF, QSize, Qt
from PyQt5 import QtGui
import CSWidgets
from PyQt5.QtWidgets import QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
#from PyQt5.QtChart import QChart, QChartView, QPieSeries
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QFont
CustomObjectRole = QtCore.Qt.UserRole + 1
import pickle
from collections import deque
from PyQt5.QtWidgets import QHBoxLayout,QPushButton,QFileDialog,QLabel,QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsPixmapItem, QGraphicsRectItem, QGraphicsTextItem, QGraphicsView, QGraphicsScene, QMessageBox,QProgressBar,QVBoxLayout,QWidget
from openai import AzureOpenAI
load_dotenv()
api_base = os.getenv("API_BASE")
api_key= os.getenv("AZURE_OPENAI_API_KEY")
deployment_name = os.getenv("DEPLOYMENT_NAME")
api_version = os.getenv("API_VERSION") # this might change in the future
#print(image)
from cloud_emulator.lambda_makespan_cost import compute_lambda_latency_and_cost
from cloud_emulator.s3_makespan_cost import compute_s3_latency_and_cost
from cloud_emulator.dynamodb_makespan_cost import compute_dynamodb_latency_and_cost
from cloud_emulator.retail_workflow_makespan_cost import compute_eks_latency_cost,compute_elasticache_latency_cost

client = AzureOpenAI(
    api_key=api_key,  
    api_version=api_version,
    base_url=f"{api_base}/openai/deployments/{deployment_name}",
)


import sys
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableView, QVBoxLayout, QPushButton,
    QLineEdit, QComboBox, QWidget, QHBoxLayout, QDialog
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QStandardItemModel, QStandardItem


class DataFrameViewer(QMainWindow):
    def __init__(self, df, parent=None):
        super().__init__(parent)
        self.df = df
        self.filtered_df = df.copy()  # Copy for modifications
        self.initUI()

    def initUI(self):
        self.setWindowTitle("DataFrame Viewer")
        self.resize(1200, 600)  # Set wider width for the table

       # Central widget
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)

        # Main layout
        self.layout = QVBoxLayout(self.central_widget)


        # Table view
        self.table_view = QTableView()
        self.table_view.setWordWrap(True)  # Enable word wrap
        self.table_view.resizeColumnsToContents()  # Adjust column width
        self.table_view.horizontalHeader().setStretchLastSection(True)  # Stretch the last column
        self.layout.addWidget(self.table_view)

        # Controls for filtering and sorting
        controls_layout = QHBoxLayout()

        # Filter controls
        self.filter_column_selector = QComboBox()
        self.filter_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.filter_column_selector)

        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("Filter value")
        controls_layout.addWidget(self.filter_input)

        self.filter_button = QPushButton("Filter")
        self.filter_button.clicked.connect(self.apply_filter)
        controls_layout.addWidget(self.filter_button)

        # Sort controls
        self.sort_column_selector = QComboBox()
        self.sort_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.sort_column_selector)

        self.sort_button = QPushButton("Sort")
        self.sort_button.clicked.connect(self.apply_sort)
        controls_layout.addWidget(self.sort_button)

        self.layout.addLayout(controls_layout)

        # Display the table
        self.load_table(self.df)

        # Set the layout
        #self.setLayout(layout)

    def load_table(self, df):
        """Load a DataFrame into the QTableView."""
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(df.columns)

        # Set bold font for the header
        font_bold = QFont()
        font_bold.setBold(True)

        for col in range(len(df.columns)):
            model.setHeaderData(col, Qt.Horizontal, df.columns[col], Qt.DisplayRole)
            model.setHeaderData(col, Qt.Horizontal, font_bold, Qt.FontRole)

        # Populate the table
        for row in df.itertuples(index=False):
            items = [QStandardItem(str(item)) for item in row]
            for item in items:
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # Align text left and vertically centered
                item.setFont(QFont("Arial", 10))  # Set font size for cells
            model.appendRow(items)

        # Set the model to the table view
        self.table_view.setModel(model)

        # Adjust row and column sizes
        self.table_view.resizeRowsToContents()  # Adjust row height for word wrap
        self.table_view.horizontalHeader().setDefaultSectionSize(200)  # Set default column width

    def apply_filter(self):
        """Filter the table based on the selected column and input value."""
        column = self.filter_column_selector.currentText()
        value = self.filter_input.text()
        self.filtered_df = self.df[self.df[column].astype(str).str.contains(value, na=False, case=False)]
        self.load_table(self.filtered_df)

    def apply_sort(self):
        """Sort the table based on the selected column."""
        column = self.sort_column_selector.currentText()
        self.filtered_df = self.filtered_df.sort_values(by=column, ascending=True)
        self.load_table(self.filtered_df)


'''
class DataFrameViewer(QMainWindow):
    def __init__(self, df, parent=None):
        super().__init__(parent)
        self.df = df
        self.filtered_df = df.copy()  # Copy for modifications
        self.initUI()

    def initUI(self):
        self.setWindowTitle("Grouped Bar Graph and Data Table in PyQt5")
        self.setGeometry(20, 20, 800, 600)
        self.setWindowTitle("DataFrame Viewer")
        self.resize(800, 600)

        # Central widget
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)

        # Main layout
        self.layout = QVBoxLayout(self.central_widget)

        # Table view
        self.table_view = QTableView()
        self.layout.addWidget(self.table_view)

        # Controls for filtering and sorting
        controls_layout = QHBoxLayout()

        # Filter controls
        self.filter_column_selector = QComboBox()
        self.filter_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.filter_column_selector)

        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("Filter value")
        controls_layout.addWidget(self.filter_input)

        self.filter_button = QPushButton("Filter")
        self.filter_button.clicked.connect(self.apply_filter)
        controls_layout.addWidget(self.filter_button)

        # Sort controls
        self.sort_column_selector = QComboBox()
        self.sort_column_selector.addItems(self.df.columns)
        controls_layout.addWidget(self.sort_column_selector)

        self.sort_button = QPushButton("Sort")
        self.sort_button.clicked.connect(self.apply_sort)
        controls_layout.addWidget(self.sort_button)

        self.layout.addLayout(controls_layout)

        # Display the table
        self.load_table(self.df)

    def load_table(self, df):
        """Load a DataFrame into the QTableView."""
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(df.columns)

        for row in df.itertuples(index=False):
            items = [QStandardItem(str(item)) for item in row]
            model.appendRow(items)

        self.table_view.setModel(model)

    def apply_filter(self):
        """Filter the table based on the selected column and input value."""
        column = self.filter_column_selector.currentText()
        value = self.filter_input.text()
        self.filtered_df = self.df[self.df[column].astype(str).str.contains(value, na=False, case=False)]
        self.load_table(self.filtered_df)

    def apply_sort(self):
        """Sort the table based on the selected column."""
        column = self.sort_column_selector.currentText()
        self.filtered_df = self.filtered_df.sort_values(by=column, ascending=True)
        self.load_table(self.filtered_df)

'''
class GraphicView(QGraphicsView):  #Canvas for inserting nodes and creating pipeline
    classmapping={}
    def __init__(self):
        super().__init__()

        self.scene = QGraphicsScene()
        self.setScene(self.scene)       
        self.setSceneRect(0, 0, 2400, 2000)
        self.shape = None
        self.cats={}
        self.prevShape = None
        self.connectorActive = False
        self.setViewportUpdateMode(QGraphicsView.FullViewportUpdate)
        self.configs = []
        self.tfrs = []
        self.setAcceptDrops(True)
        self.adjacency_matrix = []
        self.selectedservs=[]
        self.vpcbuttons=[]
        self.archtype=None
        self.mappings={}
        self.vpclist={}
        self.vpcsquares={}
        #self.vpcbuttons=[]
        self.dotted_pixmap = self.create_dotted_pixmap(dot_color=QColor(0x888888), dot_radius=2, dot_spacing=20, background_color = QColor(0xf0f0f0))
        self.set_dotted_background()

    def create_dotted_pixmap(self, dot_color, dot_radius, dot_spacing, background_color):
         # Create a pixmap with the dot and transparent background
         dot_size = dot_spacing  # Tile the dot + space
         pixmap = QPixmap(dot_size, dot_size)
         pixmap.fill(Qt.transparent)  # Transparent background for the tile

         painter = QPainter(pixmap)
         painter.setRenderHint(QPainter.Antialiasing)  # For smoother dots
         painter.setBrush(dot_color)
         painter.setPen(Qt.NoPen)
         center = dot_size // 2
         painter.drawEllipse(center - dot_radius // 2, center-dot_radius //2 , dot_radius, dot_radius)  # Draw the dot at the center of tile
         painter.end()

         return pixmap

    def set_dotted_background(self):
         # Prepare the final tiled pixmap for the entire widget
        widget_width = self.viewport().width()
        widget_height = self.viewport().height()

        tiled_pixmap = QPixmap(widget_width, widget_height)
        tiled_pixmap.fill(QColor(0xffffff)) # fill with background color
        painter = QPainter(tiled_pixmap)
        dot_size = self.dotted_pixmap.width()
        for y in range(0, widget_height, dot_size):
            for x in range(0, widget_width, dot_size):
                painter.drawPixmap(x, y, self.dotted_pixmap)
        painter.end()

        self.setBackgroundBrush(QtGui.QBrush(tiled_pixmap))


    def resizeEvent(self, event):
        # Update the dotted background pixmap on resizing
        self.set_dotted_background()
        super().resizeEvent(event)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().hasImage():
            event.acceptProposedAction()  # Accept the drop event
        else:
            event.ignore()

    def dropEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().hasImage():
            text = event.mimeData().text()
            image = event.mimeData().imageData()
            icon_label = QLabel(text, self)
            icon_label.setPixmap(QPixmap.fromImage(image))
            icon_label.move(event.pos())
            event.setDropAction(Qt.MoveAction)
            event.accept()
        else:
            event.ignore()

    def addObjectModel(self,model):
        self.shape = ModelShape(model)

    # def addStorage(self,storageType):
    #     self.shape = StorageShape(storageType)

    def addObject(self,text:str,a,b,cost,latency,type1):
        self.shape = Shape(text,a,b,cost,latency,type1,diagram)
        Widgets.widget5.clearSelection()

    def addObject(self,shape):
        self.shape = shape
        #CSWidgets.widget5.clearSelection()

    from collections import defaultdict

    def longest_path_next_node(adj_list, start_node):
    # Cache for storing the longest path from each node
      longest_path_cache = {}

      def dfs(node):
          """DFS to calculate the longest path from the current node."""
          if node in longest_path_cache:
              return longest_path_cache[node]

          if not adj_list[node]:  # If there are no outgoing edges
              longest_path_cache[node] = (0, None)  # No next node
              return longest_path_cache[node]

          max_length = 0
          next_node = None

          for neighbor in adj_list[node]:
              path_length, _ = dfs(neighbor)
              if path_length + 1 > max_length:
                  max_length = path_length + 1
                  next_node = neighbor

          longest_path_cache[node] = (max_length, next_node)
          return longest_path_cache[node]

      # Compute the longest path from the start_node
      _, next_node = dfs(start_node)
      return next_node

    
    def bfs_with_parents(self,adj_list, start_node):
      # Initialize a queue for BFS and a list to store the result
      queue = deque([start_node])
      visited = {start_node}  # Set to track visited nodes
      result = [(start_node, None)]  # Start node has no parent
      
      while queue:
          node = queue.popleft()
          
          # Explore all neighbors of the current node
          for neighbor in adj_list.get(node, []):
              if neighbor not in visited:  # If neighbor hasn't been visited
                  visited.add(neighbor)  # Mark as visited
                  result.append((neighbor, node))  # Append (neighbor, parent)
                  queue.append(neighbor)  # Add the neighbor to the queue
                  
      return result

    

    def longest_path_lengths(self, graph, node, memo):
        if node in memo:
            return memo[node]
        
        if not graph.get(node):  # Leaf node
            memo[node] = 0
            return 0
        
        max_depth = max(self.longest_path_lengths(graph, child, memo) for child in graph[node]) + 1
        memo[node] = max_depth
        return max_depth

    def bfs_ordered(self, graph, start):
        print("bfsordered")
        print(graph)
        print(start)
        memo = {}

        # Compute longest path lengths for sorting
        for node in graph:
            self.longest_path_lengths(graph, node, memo)
        
        visited = set()
        queue = deque([(start, None)])  # (node, parent)
        result = []

        while queue:
            node, parent = queue.popleft()
            if node in visited:
                continue
            visited.add(node)
            result.append((node, parent))

            if node in graph:
                sorted_children = sorted(graph[node], key=lambda x: memo[x], reverse=True)
                queue.extend((child, node) for child in sorted_children)

        # If there are unreachable nodes, return None
        #if len(visited) < len(graph):
        #    return None

        return result


    def possibilities(self,currentspotx,currentspoty):
        poss=[]
        if currentspotx==1670 and currentspoty<1820:
          poss.append((currentspotx+150,currentspoty))          
          poss.append((currentspotx,currentspoty+150))
          poss.append((currentspotx+150,currentspoty+150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty+150))         
          if currentspoty-150>=0:
            poss.append((currentspotx+150,currentspoty-150))
            poss.append((currentspotx,currentspoty-150))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ####################################3
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        elif currentspotx<1820 and currentspoty<1820:
          poss.append((currentspotx+150,currentspoty))
          poss.append((currentspotx+150,currentspoty+150))
          poss.append((currentspotx,currentspoty+150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty+150))        
          if currentspoty-150>=0:
            poss.append((currentspotx+150,currentspoty-150))
            poss.append((currentspotx,currentspoty-150))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ######################################
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        elif currentspotx>=1820 and currentspoty<1820:
          poss.append((currentspotx,currentspoty+150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty+150))
          if currentspoty-150>=0:
            poss.append((currentspotx,currentspoty-150))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          poss.append((currentspotx+150,currentspoty))
          poss.append((currentspotx+150,currentspoty+150))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ######################################
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        elif currentspotx>=1820 and currentspoty>=1820:
          poss.append((currentspotx,currentspoty))
          if currentspotx-150>=0 and currentspoty-150>=0:
            poss.append((currentspotx-150,currentspoty-150))
          poss.append((currentspotx+150,currentspoty))
          if currentspoty-150>=0:
            poss.append((currentspotx+150,currentspoty-150))
          if currentspotx-150>=0:
            poss.append((currentspotx-150,currentspoty))
          if currentspotx-300>=0 and currentspoty-150>=0:
            poss.append((currentspotx-300,currentspoty-150))
          if currentspoty-150>=0:
            poss.append((currentspotx+300,currentspoty-150))
          ######################################
          if currentspotx-300>=0:
            poss.append((currentspotx-300,currentspoty+150))
          #if currentspoty-150>=0:
          poss.append((currentspotx+300,currentspoty+150))
        return poss

    def plotOnCanvas2(self, adj_list,shapedict):
        shapeList = []
        occupiedspots=[]
        putted=[]
        #lenOfShapes = len(collShape)
        #adj_list=self.edges_to_adjacency_list(collShape)
        currentspotx=160
        currentspoty=160
        print(adj_list)
        userstr="User"
        if userstr not in shapedict:
           userstr="User_0"
        print(userstr)
        ordering=self.bfs_ordered(adj_list, userstr)
        
        print("ooooooooooooorrrrrddddddeeeeerrriiiiiiinnnnnnnnngggggg")
        print(ordering)
        self.scene.addItem(shapedict[userstr])
        text_item=QGraphicsTextItem(shapedict[userstr].name.replace("Amazon ","").replace("AWS ",""))
        text_item.setDefaultTextColor(QColor(Qt.black))
        
        text_item.setPos(QPointF(currentspotx-5,currentspoty+55))
        text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
        text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
        self.scene.addItem(text_item)
        shapedict[userstr].textItem = text_item
        shapedict[userstr].setPos(QPointF(currentspotx,currentspoty))
        shapedict[userstr].setFlag(QGraphicsTextItem.ItemIsSelectable,True)
        shapedict[userstr].setFlag(QGraphicsTextItem.ItemIsMovable,True)
        putted.append(shapedict[userstr])
        occupiedspots.append((currentspotx,currentspoty))
        #currentspotx+=150
        baapxymap={userstr:(currentspotx,currentspoty)}
        CSWidgets.button5.setEnabled(True) 
        CSWidgets.buttonsave.setEnabled(True)
        CSWidgets.buttonClear.setEnabled(True)
        CSWidgets.costlatencybutton.setEnabled(True)
        CSWidgets.selectbutton.setEnabled(True)
        #CSWidgets.deploybutton.setEnabled(True)
        CSWidgets.modelsBox.setEnabled(True)
        prevparent=None
        uniqordering=[]
        print(ordering)
        for pairu in ordering:
           if pairu not in uniqordering:
              uniqordering.append(pairu)
        print(uniqordering)
        for pair in uniqordering:
            #print(x.name)
            if userstr == pair[0]:
               continue
            
            #if prevparent!=pair[1] and pair[1]!=None:
            currentspotx,currentspoty=baapxymap[pair[1]]
            print(pair)
            #print(shapedict)
            
            
            print("baaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaap")
            print((currentspotx,currentspoty))
            posses=self.possibilities(currentspotx,currentspoty)
            print("possibilituessssssssssssssss")
            print(posses)
            pscount=0
            currcurrentspotx,currcurrentspoty=posses[pscount]
            print("spottingggggggggggggggggggggggggggggggggg")
            while (currcurrentspotx,currcurrentspoty) in occupiedspots:
              #print(pair)
              print(occupiedspots)
              print((currcurrentspotx,currcurrentspoty))
              pscount+=1
              currcurrentspotx,currcurrentspoty=posses[pscount]
              print((currcurrentspotx,currcurrentspoty))
            print("ultimatelyyyyyyyyyyyyyyy")
            print((currcurrentspotx,currcurrentspoty))
            if shapedict[pair[0]] not in putted:
              self.scene.addItem(shapedict[pair[0]])
              shapedict[pair[0]].setPos(QPointF(currcurrentspotx,currcurrentspoty))
              shapedict[pair[0]].setFlag(QGraphicsTextItem.ItemIsSelectable,True)
              shapedict[pair[0]].setFlag(QGraphicsTextItem.ItemIsMovable,True)
              text_item=QGraphicsTextItem(shapedict[pair[0]].name.replace("Amazon ","").replace("AWS ",""))
              text_item.setDefaultTextColor(QColor(Qt.black))
              
              text_item.setPos(QPointF(currcurrentspotx-15,currcurrentspoty+55))
              text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
              text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
              shapedict[pair[0]].textItem = text_item
              self.scene.addItem(text_item)
              putted.append(shapedict[pair[0]])
            occupiedspots.append((currcurrentspotx,currcurrentspoty))
            if pair[1]!=None:
              arrow = Arrow(shapedict[pair[1]],shapedict[pair[0]],True)
              self.adjacency_matrix.append([shapedict[pair[1]],shapedict[pair[0]]])
              self.scene.addItem(arrow)
            baapxymap[pair[0]]=currcurrentspotx,currcurrentspoty
            prevparent=pair[1]
    def edges_to_adjacency_list(self,edges):
          adjacency_list = {}  # Stores the final adjacency list
          in_degree = {}  # Tracks incoming edges for cycle detection

          # Step 1: Build initial adjacency list and in-degree count
          print("Input edges:", edges)
          for u, v in edges:
              if u not in adjacency_list:
                  adjacency_list[u] = []
                  in_degree[u] = 0  # Initialize in-degree

              if v not in adjacency_list:
                  adjacency_list[v] = []
                  in_degree[v] = 0  # Initialize in-degree

              # Prevent self-loops (u → u)
              if u == v:
                  continue  

              # Add edge if it doesn't create a duplicate
              if v not in adjacency_list[u]:
                  adjacency_list[u].append(v)
                  in_degree[v] += 1  # Increase in-degree for cycle detection

          print("Initial Adjacency List:", adjacency_list)
          '''
          # Step 2: Detect and remove cycles using Kahn's Algorithm
          queue = deque([node for node in in_degree if in_degree[node] == 0])  # Start with nodes having no incoming edges
          acyclic_adjacency_list = {node: [] for node in adjacency_list}  # Initialize with all nodes

          visited_nodes = set()

          while queue:
              node = queue.popleft()
              visited_nodes.add(node)

              for neighbor in adjacency_list[node]:
                  in_degree[neighbor] -= 1  # Reduce in-degree
                  if in_degree[neighbor] == 0:
                      queue.append(neighbor)  # Add new independent nodes
                  acyclic_adjacency_list[node].append(neighbor)
          '''
      # Step 3: Handle cycles
      #remaining_nodes = set(adjacency_list.keys()) - visited_nodes
      #if remaining_nodes:
      #    print(f"Cycle detected! Nodes involved: {remaining_nodes}")
      #    return adjacency_list  # Returning the original adjacency list to preserve all nodes

      #return acyclic_adjacency_list
          acyclic_adjacency_list=adjacency_list
          correct_acyclic_adjacency_list={}
          dones=[]
          print("origgg")
          print(acyclic_adjacency_list)
          donecounter={}
          for k,v in acyclic_adjacency_list.items():
              newvals=[]
              for vals in v:
                  if k!=vals:
                      if vals not in dones:
                          newvals.append(vals)
                      else:
                          counter=donecounter[vals]+1
                          newvals.append(vals+"_"+str(counter))
                          donecounter[vals]+=1
                  
              if len(newvals)>0:
                  nk=k
                  if k in dones:
                      nk=k+"_"+str(donecounter[vals])
                  correct_acyclic_adjacency_list[k]=newvals
                  dones.append(k)
                  donecounter[k]=0
          print(acyclic_adjacency_list)
          print(correct_acyclic_adjacency_list)
          return correct_acyclic_adjacency_list  # Returns a clean, cycle-free adjacency list

    def plotGraph(self,network): 
        
        #serviceList = [7,10,11,97,98]#t2.toPlainText().splitlines() 
        rownum = 0 
        #serviceList = self.t2.toPlainText().splitlines() 
        serviceList = network.splitlines()
        edgeslist=[]
        for ee in serviceList:
            pair=ee.split("-")
            edgeslist.append((pair[0].strip(),pair[1].strip()))
        adj_list=self.edges_to_adjacency_list(edgeslist)
        #collShape = [] 
        print("==============================================")
        print(adj_list)
        shapedict={}
        for node, neighbors in adj_list.items():
            import builtins
            newneighbors = builtins.list(neighbors)
            #newneighbors=list(neighbors)
            newneighbors.append(node)
            print("newneighbours")
            print(newneighbors)
            for servi in newneighbors:
                print("servi")
                print(servi)
                if servi in shapedict:
                   continue
                rownum = 0
                print("serviiiii")
                print(servi)
                serv=servi.split("_")[0]
                for iterator in awslist: 
                    rownum += 1 
                    
                    if serv.replace("- ", "") == iterator or (serv!="User" and " ".join(serv.split(" ")[1:]).strip() in iterator):
                        print("Here in PlotGraph 1 "+serv) 
                        print(rownum)
                        print(iterator)
                        shape = Shape("CSP",50,50,0,0,rownum)
                        shapedict[servi]=shape
                        #collShape.append(shape) 
                        break
                    elif serv.replace("- ", "") == iterator and (serv=="User"):
                        print("Here in PlotGraph 2 "+serv) 
                        print(rownum)
                        print(iterator)
                        shape = Shape("CSP",50,50,0,0,rownum)
                        shapedict[servi]=shape
                        #collShape.append(shape) 
                        break
                    
        print(adj_list)
        print(shapedict)
        import sys
        #sys.exit()
        self.plotOnCanvas2(adj_list,shapedict) #for x in collShape:


    def loadImage(self):
        import base64
        import openai
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        fileName, _ = QFileDialog.getOpenFileName(self,"Load Logic", ".","PNG Files (*.png)", options=options)
        with open(fileName,'rb') as file:
          encoded_image = base64.b64encode(open(fileName, 'rb').read()).decode('ascii')
        #encoded_image_example_real = base64.b64encode(open("TMCexamplereal.png", 'rb').read()).decode('ascii')
        #encoded_image = base64.b64encode(open(image, 'rb').read()).decode('ascii')
        client = openai.AzureOpenAI(
            api_key="8XHopHUcg6hikApAMUNRvXmzDiZTOuA9XBi9DfwbpiLpZ8oUHZSZJQQJ99BBACYeBjFXJ3w3AAABACOGDAY9",  # Replace with your actual Azure OpenAI API key
            api_version="2024-02-15-preview",  # GPT-4o requires this version
            azure_endpoint="https://sharodmaple.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview"  # Example: https://your-resource.openai.azure.com
        )
        def getresponse(query, outtokens=500):
          """Processes both text and an image using Azure OpenAI GPT-4o."""
          response = client.chat.completions.create(
              model="gpt-4o",  # Ensure this matches your deployed model name
              messages=[
                  {"role": "user", "content": "You are an expert AWS cloud architect. You can see an image of a cloud architechture and read and comprehend it correctly. You can also identify multiple occurances of a cloud service in the image."},
                  {"role": "user", "content": [
                      {"type": "text", "text": query},  # User's query
                      {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"}}  # Image
                  ]}
              ],
              max_tokens=outtokens
          )
          
          return response.choices[0].message.content
        print(awslist)
        nodes=getresponse("List all the cloud services and non cloud inputs present in the diagram. By non cloud inputs I mean objects that provides inputs by arrows to the cloud services by line starting from it, or receives output from cloud services by line coming into it along with a arrow head. Take care to output all non cloud input and output objects. Do not write any more text. Always in the image there must be an initial input coming into the cloud architecture and output going out of the could architecture. Always model this input and output as non cloud service 'User'. So your reply must and must contain this. Dont miss non cloud services. Do not provide any explanation. Do not write any prefix sentences. Do not provide numbering of the services. Just write the service names one after the other seperated by newline only, not comma etc. Predict the unique set of cloud services in the image. The services you predict, should be always from the valid AWS services list given below. Do not predict a service that is not in the valid AWS services list. And the text you predict should be ditto from the valid AWS services list. Do not change a word, or do not shorten the text of any service. The list is:\n "+"\n".join(awslist) ,500).split("\n")
        newnodes=[]
        print(nodes)
        for n in nodes:
          if "cloud services" in n.lower() or "non-cloud" in n.lower() or len(n.strip())==0:
            continue
          newnodes.append(n.replace("- ","").strip())
        nodes=newnodes
        nodecount=getresponse("For each of the cloud service only in the list, mention the count or number of them occuring in the image. So basically you have to say for each of the text in the list below, how many times that text occur in the image. The output format should be each service in each line, followed by a dash '-' and then its count. Do not write any other text. Do not write any prefix or suffix text. The list is:\n "+"\n".join(nodes)).split("\n")
        newnewnodelist=[]
        for nodu in nodecount:
          if int(nodu.split("-")[-1])==1:
            newnewnodelist.append(nodu.split("-")[0])
          else:
             for intu in range(int(nodu.split("-")[-1])):
                newnewnodelist.append(nodu.split("-")[0]+"_"+str(intu))
        print("nnnnnnnnnewwwwwwwwwwwnnnnnnnnnnnewwwwwwwww")
        print(newnewnodelist)
        tons=getresponse("The services in the diagram are directly connected by a single arrow, originating from it, in 1 step to 1 other service in the diagram. Remember that there is always only a single target service. By an arrow originating from one service, I mean that the tail of the arrow (not having the arrow head), is attached to the other service. While the head of the arrow containing the arrow head in attached to those services or box in the diagram. Be very very careful about the direction of arrows. One service may have multiple arrows originating from it. I want a list of all such arrows in the image, outputted as pairs of services. A service can occur multiple times in the image. For that, the list will have repetitions of the same servive followed by an underscore and a unique count. So match a service in the image with a service followed by an underscore and respective counter in the list. Thus while outputting the service pairs take care to check which service followed by underscore and counter is part of which arrow. Dont mix this up. For example Amazon EC2, if there are multiple EC2, make sure you note from which EC2, each arrow is originating from or ending to.  Take care and mention all of them. Always in the image there must be an initial input coming into the cloud architecture and output going out of the could architecture. This input and out is always the 'User' service in the list, maybe followed by underscore and counter. So the must and absolutely must be an input arrow in your replies from one of the 'User' service and an output to the same or other 'User' service.  Do not write any more text or dashes or numbers. Do not provide any explanation. I do not want to know anything extra. Just output the respective input service followed by '-' and followed by target services. Each and every service mentioned in the list must feature in your edges list. Dont miss anyone. Dont miss even a service which is mentioned multiple times followed by underscore and counter. For example: if there is Amazon EC2_0 and Amazon EC2_1 in the list. Both must and definitely must feature in your output. Dont miss one or more of them. Take very close care that, the graph you thus output, should be fully connected. This means that, in the graph you output, ther should always be a path from the starting service which is always User_0. Take very good care of this. The complete service list is: "+str(newnewnodelist),1000)
        tonscorr=""
        for ton in tons.split("\n"):
           if "-" in ton:
              tonscorr+=ton+"\n"
        print(tonscorr)
        self.plotGraph(tonscorr)


    def plotOnCanvas(self, collShape):
        print("Enered into Plotting")
        xCord = 50
        yCord = 100
        shifter = 0
        xcounter=0
        xsign=1
        ysign=1
        shapeList = []
        lenOfShapes = len(collShape)
        for x in collShape:
            print(x.name)
            self.scene.addItem(x)
            x.setPos(QPointF(xCord,yCord))
            if xcounter>0 and xcounter%4==0:
              xsign=xsign*-1
              yCord+=150
            else:
              xCord = xCord + 150*xsign
            xcounter+=1
            #yCord=yCord+(30*ysign)
            #ysign=ysign*-1
            shapeList.append(x)
            print(self.scene.items().__len__())
            print(self.scene.items())
            length = self.scene.items().__len__()
            if length <= 2:
                continue
            if length > 2 and length <= 4:
                #arrow = Arrow(self.scene.itemAt(self.scene.items().__len__()-2),self.scene.itemAt(self.scene.items().__len__()))
                arrow = Arrow(self.scene.items()[length-1],self.scene.items()[length-3],True)
                #arrow = Arrow(self.scene.items()[1],self.scene.items()[3],True)
                self.scene.addItem(arrow)
                print("------IF" + str(self.scene.items().__len__()))
            else:
                arrow = Arrow(self.scene.items()[length-(2+(3*shifter))],self.scene.items()[length-(4+(3*shifter))],True)
                self.scene.addItem(arrow)
                print("------REST"+ str(self.scene.items().__len__()))
                shifter = shifter + 1
            
            '''
            elif length > 4 and length <= 7:
                arrow = Arrow(self.scene.items()[length-2],self.scene.items()[length-4],True)
                self.scene.addItem(arrow)
                print("------ELSE")
            elif length > 7 and length <= 11:
                arrow = Arrow(self.scene.items()[length-5],self.scene.items()[length-7],True)
                self.scene.addItem(arrow)
                print("------ELSE222")
            elif self.scene.items().__len__() > 11:
                arrow = Arrow(self.scene.items()[length-8],self.scene.items()[length-10],True)
                self.scene.addItem(arrow)
                print("------ELSE222")
            else:
                arrow = Arrow(self.scene.items()[length-(2+(3*shifter))],self.scene.items()[length-(4+(3*shifter))],True)
                self.scene.addItem(arrow)
                print("------REST")
            '''    
            #shifter = shifter + 1
        arrow = Arrow(self.scene.items()[length-(2+(3*shifter)-1)],self.scene.items()[length-(4+(3*shifter)-1)],True)
        #arrow = Arrow(self.scene.items()[length-10],self.scene.items()[length-12],True)
        self.scene.addItem(arrow)

        for i in range(lenOfShapes):
          text_item=QGraphicsTextItem(shapeList[i].name.replace("Amazon ","").replace("Simple Storage System (S3)","Amazon S3"))
          text_item.setDefaultTextColor(QColor(Qt.black))
          text_x=shapeList[i].x()-(shapeList[i].a)/2
          text_y=shapeList[i].y()+shapeList[i].b+5
          text_item.setPos(QPointF(text_x,text_y))
          text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
          text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
          self.scene.addItem(text_item)
          shapeList[i].textItem = text_item
        '''
        arrow = Arrow(self.scene.items()[1],self.scene.items()[3],True)
        self.scene.addItem(arrow)
        arrow = Arrow(self.scene.items()[3],self.scene.items()[5],True)
        self.scene.addItem(arrow)
        arrow = Arrow(self.scene.items()[5],self.scene.items()[7],True)
        self.scene.addItem(arrow)
        arrow = Arrow(self.scene.items()[7],self.scene.items()[9],True)
        self.scene.addItem(arrow)
        '''

    def mouseDoubleClickEvent(self, event): #add object in self.shape to location clicked, if location is empty
        print("-------------------"+self.shape.name)
        position = self.mapToScene(event.pos())
        grp = ""
        #print("scene length "+str(self.scene.items().__len__()))
        if(self.shape.group is not None):
            grp = self.shape.group
        if(self.scene.items().__len__() < 1):    
            if(self.shape is not None):
                if(self.itemAt(int(position.x()),int(position.y())) is None):
                    if(self.prevShape is None or self.prevShape != self.shape):
                        self.scene.addItem(self.shape)
                        self.shape.setPos(QPointF(position.x(), position.y()))
                        self.scene.addItem(self.shape.proxy)
                        self.shape.proxy.setPos(QPointF(position.x()-10, position.y()))
                        text_item=QGraphicsTextItem(self.shape.name)
                        text_item.setDefaultTextColor(QColor(Qt.black))
                        font = QFont()
                        font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                        text_item.setFont(font)
                        text_x=position.x()
                        text_y=position.y()+self.shape.b+5;
                        text_item.setPos(QPointF(text_x,text_y))
                        text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                        text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                        self.scene.addItem(text_item)
                        self.shape.textItem = text_item
        else:
            if self.shape.group in self.cats:
                # print(self.shape.group in self.cats)
                reply = QMessageBox.question(self, 'Warning', "You have already selected "+self.cats[self.shape.group][0]+" for the purpose of "+self.shape.group+" Would you like to continue?" ,QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    if self.shape is not None:
                        if(self.itemAt(int(position.x()),int(position.y())) is None):
                            if(self.prevShape is None or self.prevShape != self.shape):
                                self.scene.addItem(self.shape)
                                self.cats[self.shape.group] = [self.shape.name]
                                self.shape.setPos(QPointF(position.x(), position.y()))
                                self.scene.addItem(self.shape.proxy)
                                self.shape.proxy.setPos(QPointF(position.x()-10, position.y()))
                                text_item=QGraphicsTextItem(self.shape.name)
                                text_item.setDefaultTextColor(QColor(Qt.black))
                                text_x=position.x()
                                text_y=position.y()+self.shape.b+5;
                                text_item.setPos(QPointF(text_x,text_y))
                                text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                                font = QFont()
                                font.setPointSize(15)  # Increase the font size to 16

                                # Apply the font to the QGraphicsTextItem
                                text_item.setFont(font)
                                # text_item.setFlag(QGraphicsTextItem.ItemIsFocusable,True)
                                text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                                self.scene.addItem(text_item)
                                self.shape.textItem = text_item
            else:
                print(self.shape.group)
                if(self.shape is not None):
                    if(self.itemAt(int(position.x()),int(position.y())) is None):
                        if(self.prevShape is None or self.prevShape != self.shape):
                            self.scene.addItem(self.shape)
                            
                            self.cats[self.shape.group] = [self.shape.name]
                            self.shape.setPos(QPointF(position.x(), position.y()))
                            self.scene.addItem(self.shape.proxy)
                            self.shape.proxy.setPos(QPointF(position.x()-10, position.y()))
                            text_item=QGraphicsTextItem(self.shape.name)
                            text_item.setDefaultTextColor(QColor(Qt.black))
                            text_x=position.x()
                            text_y=position.y()+self.shape.b+5;
                            font = QFont()
                            font.setPointSize(15)  # Increase the font size to 16

                            # Apply the font to the QGraphicsTextItem
                            text_item.setFont(font)
                            text_item.setPos(QPointF(text_x,text_y))
                            text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                            text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                            # print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
                            self.scene.addItem(text_item)
                            self.shape.textItem = text_item
            
            
        CSWidgets.button5.setEnabled(True)
        CSWidgets.buttonsave.setEnabled(True)
        CSWidgets.costlatencybutton.setEnabled(True)
        #CSWidgets.deploybutton.setEnabled(True)
        CSWidgets.selectbutton.setEnabled(True)
        CSWidgets.labelmarquee.setText(" :: Select first and second cloud service and use 'Connector' button to establish connection between them.")
        CSWidgets.buttonClear.setEnabled(True)
        CSWidgets.modelsBox.setEnabled(True)
        CSWidgets.labelmarquee.setText(" :: To identify next suitable cloud service please Connect with AI or select pre-decided service from left side pallete and plot on canvas using double click.")

        #if(self.shape is not None):
        #    if(self.itemAt(int(position.x()),int(position.y())) is None):
        #        if(self.prevShape is None or self.prevShape != self.shape): 
        #            self.scene.addItem(self.shape)
        #            self.shape.setPos(QPointF(position.x(), position.y()))
        #            if(self.shape.type() == QGraphicsRectItem().type()):
        #                self.shape.configs = self.configs
        #                Widgets.widget5.clearSelection()
        #                Widgets.labelmarquee.setText(" Tip :: Select elements on canvas in order and use 'Connector' button to establish connection between them.")
        #            elif(self.shape.type() == QGraphicsEllipseItem().type()):
        #                self.shape.tfrs = self.tfrs
        #                if(self.shape.text == "I"):
        #                    Widgets.labelmarquee.setText(" Tip :: Select required model from 'Applicable Models' left hand side list and insert in canvas.")
        #                    Widgets.domainsBox.setEnabled(True)
        #                    Widgets.domainlist.setEnabled(True)
        #                if(self.shape.text == "O"):
        #                    Widgets.labelmarquee.setText(" Tip :: Select any deployment resource and check prediction numbers.")    
        #            self.prevShape = self.shape
                    
                    
    def addArrow(self,arr=None): #add arrow from first object clicked to second object clicked, connected to 'Connector' button
        items = self.scene.selectedItems()
        print(arr)
        shapeItems = [item for item in items if (isinstance(item,Shape))]
        if(len(shapeItems) == 2):
            if(shapeItems[0].selectedTime < shapeItems[1].selectedTime):
                start_item = shapeItems[0]
                end_item = shapeItems[1]
            else:
                start_item = shapeItems[1]
                end_item = shapeItems[0]
            if((shapeItems[0].type() == QGraphicsRectItem().type()) & (shapeItems[1].type() == QGraphicsRectItem().type())):
                #selector = Shape("",20,20,0,0,"")
                transformer = Shape("T",30,30,0,0,"")
                self.scene.addItem(transformer)
                transformer.tfrs = self.tfrs
                arrow1 = Arrow(start_item,transformer)
                arrow2 = Arrow(transformer,end_item)
                transformer.setPos((start_item.scenePos()+end_item.scenePos())/2)
                self.scene.addItem(arrow1)
                self.scene.addItem(arrow2)
                #Widgets.button4.setEnabled(True)
                Widgets.widget7.setEnabled(True)
                Widgets.add_tfr_button.setEnabled(True)
                Widgets.edit_tfr_button.setEnabled(True)
            else:
                if arr==None:
                  arrow = Arrow(start_item,end_item,True)
                elif not isinstance(arr,Arrow):
                   arrow = Arrow(start_item,end_item,True)
                   arrow.Cost=arr[0]
                   arrow.Latency=arr[1]
                   arrow.attributes=arr[2]
                else:
                   print("arrowwwww")
                   arrow=arr
                   print(arrow.Cost)
                print(arrow)
                self.scene.addItem(arrow)
                self.scene.addItem(arrow.proxy)
                self.adjacency_matrix.append([start_item,end_item,arrow])
                #GraphicView.netattributes.append({"source_region":start_item.region,"destination_region":end_item.region,"data_size":0,"bandwidth":0})
                #GraphicView.netcostlat_matrix.append((0,0))
                # transformer = Shape("",0,0,0,0,1)
                # arrow1 = Arrow(start_item,transformer,False)
                # arrow2 = Arrow(transformer,end_item,True)
                
                # transformer.setPos(start_item.pos1().x(),end_item.pos1().y())
                # self.scene.addItem(arrow1)
                # self.scene.addItem(arrow2)

                if(end_item.type() != QGraphicsRectItem().type()):
                    if(end_item.text == "O"):
                        Widgets.buttonsave.setEnabled(True)
                        Widgets.DR.setEnabled(True)
                        if(Widgets.deploymentMode=="local"):
                            Widgets.widget6.setEnabled(True)
                            Widgets.serverlabel.setEnabled(True)
                            Widgets.buttonconfig.setEnabled(True)


            shapeItems[0].setSelected(False)
            shapeItems[1].setSelected(False)
            for item in items:
                item.setSelected(False)
        #self.calcCostLatency()
    def addArrowopt(self,shape1,shape2,updateadj): #add arrow from first object clicked to second object clicked, connected to 'Connector' button
        #items = self.scene.selectedItems()
        shapeItems = [shape1,shape2]
        print(shapeItems)
        if(len(shapeItems) == 2):
            #if(shapeItems[0].selectedTime < shapeItems[1].selectedTime):
            #    start_item = shapeItems[0]
            #    end_item = shapeItems[1]
            #else:
            start_item = shapeItems[0]
            end_item = shapeItems[1]
            if((isinstance(shapeItems[0],Shape)) & (isinstance(shapeItems[1],Shape))):
                arrow = Arrow(start_item,end_item,True)
                print("o my god...................................")
                self.scene.addItem(arrow) 
                print("tinkujia.........................................")         
                if updateadj:
                  self.adjacency_matrix.append([start_item,end_item,arrow])
                  #GraphicView.netattributes.append({"source_region":start_item.region,"destination_region":end_item.region,"data_size":0,"bandwidth":0})
                  #GraphicView.netcostlat_matrix.append((0,0))
                
        #self.calcCostLatency()
        print("done**************************************************")

    def updateCloudLatency1(self, model, latency):#Created for sake of demo only
        totalcost = 0
        for x in range(self.scene.items().__len__()):
            if(self.scene.items().__getitem__(x).type() == QGraphicsRectItem().type()):
                if(self.scene.items().__getitem__(x).model.Model == model):
                    self.scene.items().__getitem__(x).model.CloudLatency = latency
                    self.scene.items().__getitem__(x).CloudLatency = latency
                    print(self.scene.items().__getitem__(x).model.CloudLatency)
        print('Updated Total Cost - ',totalcost)
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                print('Cloud Latency ',item.model.CloudLatency)
        return totalcost


    def updateCloudLatency(self, model, storage):
        totalcost = 0
        for x in range(self.scene.items().__len__()):
            if(self.scene.items().__getitem__(x).type() == QGraphicsRectItem().type()):
                if(self.scene.items().__getitem__(x).model.Model == model):
                    self.scene.items().__getitem__(x).model.CloudLatency = self.scene.items().__getitem__(x).model.InitialLatency * (self.scene.items().__getitem__(x).model.Memory/storage)
                    self.scene.items().__getitem__(x).CloudLatency = self.scene.items().__getitem__(x).model.InitialLatency * (self.scene.items().__getitem__(x).model.Memory/storage)
                    print(self.scene.items().__getitem__(x).model.CloudLatency)
                    #self.scene.items().__getitem__(x).Cores = cores
                    #self.scene.items().__getitem__(x).Cost *= self.scene.items().__getitem__(x).Cores/self.scene.items().__getitem__(x).InitialCores
                    #self.scene.items().__getitem__(x).Latency *= self.scene.items().__getitem__(x).InitialCores/self.scene.items().__getitem__(x).Cores
        print('Updated Total Cost - ',totalcost)
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                print('Cloud Latency ',item.model.CloudLatency)
        return totalcost
    
    def getTotalCost(self, cores):
        #Calculation of extrapolated Cost and Latency in below FOR LOOP
        for x in range(self.scene.items().__len__()):
            if(self.scene.items().__getitem__(x).type() == QGraphicsRectItem().type()):
                if(self.scene.items().__getitem__(x).model.Cores != cores):
                    self.scene.items().__getitem__(x).model.Cores = cores
                    icost = self.scene.items().__getitem__(x).model.InitialCost
                    ilatency = self.scene.items().__getitem__(x).model.InitialLatency
                    self.scene.items().__getitem__(x).Cost = icost * self.scene.items().__getitem__(x).model.Cores/self.scene.items().__getitem__(x).model.InitialCores
                    self.scene.items().__getitem__(x).Latency = ilatency * self.scene.items().__getitem__(x).model.InitialCores/self.scene.items().__getitem__(x).model.Cores
        
        items = self.scene.items()
        totalcost = 0
        for item in items:
            if(item.type() == QGraphicsRectItem().type() or item.type() == QGraphicsEllipseItem().type()):
                totalcost += item.Cost
        return totalcost

    def keyPressEvent(self, event): #for deleting object from scene. Error: object gets removed but not deallocated
        if event.key() == Qt.Key_Delete:#Backspace:
            items = self.scene.selectedItems()
            for item in items:
                self.scene.removeItem(item)
                if(item.type() != QGraphicsLineItem().type()):
                    for arrow in item.in_arrows:
                        self.scene.removeItem(arrow)
                    for arrow in item.out_arrows:
                        self.scene.removeItem(arrow)
        #self.calcCostLatency()



    def create_dual_pie_chart_window(self,data1, data2):
        """
        Creates a new window displaying two pie charts based on `data1` and `data2`.
        A single legend is shared, and totals for each chart are prominently displayed above their respective pie charts.

        Parameters:
            data1 (dict): A dictionary where keys are text labels for the slices, and values are their numerical values for the first chart.
            data2 (dict): A dictionary where keys are text labels for the slices, and values are their numerical values for the second chart.
        """
        # Validate the inputs
        if not (data1 and data2) or not (data1.keys() == data2.keys()):
            raise ValueError("Both dictionaries must have the same keys.")
        if not all(isinstance(k, str) and isinstance(v, (int, float)) and v > 0 for k, v in data1.items()):
            raise ValueError("Data1 must be a dictionary with string keys and positive numerical values.")
        if not all(isinstance(k, str) and isinstance(v, (int, float)) and v > 0 for k, v in data2.items()):
            raise ValueError("Data2 must be a dictionary with string keys and positive numerical values.")

        # Calculate the totals
        total1 = sum(data1.values())
        total2 = sum(data2.values())

        # Create a new QMainWindow for the pie charts
        pie_window = QMainWindow()
        pie_window.setWindowTitle("Dual Pie Chart Window")
        pie_window.resize(800, 600)

        # Create pie series for each data set
        series1 = QPieSeries()
        series2 = QPieSeries()
        for label, value in data1.items():
            series1.append(label, value)
            series2.append(label, data2[label])

        # Create charts for each series
        chart1 = QChart()
        chart1.addSeries(series1)
        chart1.setTitle("Breakdown of Total 1")
        chart1.legend().setVisible(False)  # Hide individual legends

        chart2 = QChart()
        chart2.addSeries(series2)
        chart2.setTitle("Breakdown of Total 2")
        chart2.legend().setVisible(False)  # Hide individual legends

        # Create a single shared legend
        shared_legend = QChart()
        shared_legend.addSeries(series1)  # Use one of the series for the legend
        shared_legend.legend().setVisible(True)
        shared_legend.legend().setAlignment(Qt.AlignBottom)

        # Create chart views for each chart
        chart_view1 = QChartView(chart1)
        chart_view1.setRenderHint(QPainter.Antialiasing)
        chart_view2 = QChartView(chart2)
        chart_view2.setRenderHint(QPainter.Antialiasing)

        # Create labels to display the totals
        total_label1 = QLabel(f"Total 1: {total1}")
        total_label1.setAlignment(Qt.AlignCenter)
        total_label1.setFont(QFont("Arial", 14, QFont.Bold))

        total_label2 = QLabel(f"Total 2: {total2}")
        total_label2.setAlignment(Qt.AlignCenter)
        total_label2.setFont(QFont("Arial", 14, QFont.Bold))

        # Create an OK button
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(pie_window.close)  # Connect the button to close the window

        # Layouts
        layout = QVBoxLayout()
        
        # Add first pie chart and total
        chart1_layout = QVBoxLayout()
        chart1_layout.addWidget(total_label1)
        chart1_layout.addWidget(chart_view1)

        # Add second pie chart and total
        chart2_layout = QVBoxLayout()
        chart2_layout.addWidget(total_label2)
        chart2_layout.addWidget(chart_view2)

        # Combine the two charts horizontally
        charts_layout = QHBoxLayout()
        charts_layout.addLayout(chart1_layout)
        charts_layout.addLayout(chart2_layout)

        layout.addLayout(charts_layout)
        layout.addWidget(ok_button)  # Add the OK button at the bottom

        # Create central widget
        central_widget = QWidget()
        central_widget.setLayout(layout)
        pie_window.setCentralWidget(central_widget)

        # Show the pie chart window
        pie_window.show()

        return pie_window  # Return the window in case further manipulation is needed


    def calcLat(self,selectedservs,type,arrl,arrc):
      print(type)
      print(self.archtype)
      costdict={}
      latdict={}
      if type=="Monolith":
        self.selectedservs=selectedservs
        tlate=0.0
        tcost=0.0
        print(self.selectedservs)
        for item in self.scene.items():
          if isinstance(item,Shape) and item.name in self.selectedservs :
              print(item.name,end="----")
              print( item.Latency,end="   ")
              print( item.Cost)
              tlate += item.Latency
              tcost+=item.Cost
              costdict[item.name]=item.Cost
              latdict[item.name]=item.Latency

          '''     
          for x in self.selectedservs:
            for y in self.selectedservs:
                for pair in self.adjacency_matrix:
                  if pair[0].name==x and pair[1].name==y:
                      tlate+=pair[2].Latency
                      tcost+=pair[2].Cost
                      costdict[x+"-"+y+" network"]=pair[2].Cost
                      latdict[x+"-"+y+" network"]=pair[2].Latency
                      '''
        tlate+=arrl
        tcost+=arrc
        for vpc in self.selectedservs:
           if "VPC" in vpc:
              tcost+=float(self.vpclist[int(vpc.split(" ")[-1])]["cost"].split(" ")[0])
              costdict[vpc]=float(self.vpclist[int(vpc.split(" ")[-1])]["cost"].split(" ")[0])
        #global totLateDisp
        CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tlate))+" sec")
        CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tcost))+" $")
        #self.create_dual_pie_chart_window(costdict, latdict)
      else:
        #self.selectedservs=selectedservs
        tlate=0.0
        self.formGroupBox2 = QGroupBox()
        self.formGroupBox2.setWindowTitle("Input Params")
        self.formGroupBox2.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layout2 = QFormLayout()

        self.c22 = QLabel("Please enter Architechture Parameters:")
        self.c22.setAlignment(Qt.AlignCenter)
        self.layout2.addRow(self.c22)
        self.label2 = QLabel("Concurrency: ")
        self.text_box2 = QLineEdit("100")
        self.layout2.addRow(self.label2,self.text_box2)
        #self.label2 = QLabel("Workflow: ")
        #self.text_box2 = QComboBox()
        #self.text_box2 = QLineEdit("1")
        #self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("MySQL Instance Type: ")
        self.text_box2 = QLineEdit("db.t3.medium")
        self.text_box2 = QComboBox()
        self.text_box2.addItems(["db.t3.medium","db.t3.large","db.r5.large"])
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("Elasticache Instance Type: ")
        self.text_box2 = QComboBox()
        self.text_box2.addItems(['t3.micro', 't2.micro', 't3.small', 't2.small'])
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("EKS Num Nodes: ")
        self.text_box2 = QLineEdit("1")
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("EKS Instance Type: ")
        self.text_box2 = QLineEdit("m5.large")
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("Postgresql Instance Type: ")
        self.text_box2 = QLineEdit("db.t3.medium")
        self.layout2.addRow(self.label2,self.text_box2)
        self.label2 = QLabel("MQ Instance Type: ")
        self.text_box2 = QLineEdit("t3.micro")
        self.layout2.addRow(self.label2,self.text_box2)
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.finallat)
        cancel_button.clicked.connect(self.returner)
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        self.layout2.addRow(HBox)
        self.formGroupBox2.setLayout(self.layout2)
        self.formGroupBox2.show()

    def open_table_viewer(self):
        # Example DataFrame
        elasticache={"1,1":"C3_T1","1,2":"C3_T2","2,1":"C3_T3","2,2":"C3_T4","2,0.5":"C3_T5","2,1.37":"C3_T6","2,2.79":"C3_T7","2,4":"C3_T8","2,8":"C3_T9","2,4":"C3_T10","2,8":"C3_T11","4,16":"C3_T12","2,8":"C3_T13","4,16":"C3_T14","2,8":"C3_T15","4,16":"C3_T16","2,8":"C3_T17","4,16":"C3_T18","2,8":"C3_T19","4,16":"C3_T20","2,16":"C3_T21","4,32":"C3_T22","2,16":"C3_T23","4,32":"C3_T24","2,16":"C3_T25","4,32":"C3_T26","2,16":"C3_T27","4,32":"C3_T28","2,15.25":"C3_T29","4,30.5":"C3_T30"}
        ec2_10={"2,8":"C1_T1","4,16":"C1_T14","8,32":"C1_T15","16,64":"C1_T16","2,16":"C1_T17","4,32":"C1_T18","8,64":"C1_T19"}
        ec2_5={"2,1":"C1_T8","2,2":"C1_T9","2,4":"C1_T10","2,8":"C1_T11","4,16":"C1_T12","8,32":"C1_T13"}
        ec2_l={"1,1":"C1_T2","1,2":"C1_T3","2,4":"C1_T4","2,8":"C1_T5","4,16":"C1_T6","8,32":"C1_T7"}
        #aurora={"2,4":"C2_T1","2,8":"C2_T2","2,16":"C2_T3","2,1":"C2_T4","2,2":"C2_T5","2,2":"C2_T6","2,4":"C2_T7","2,8":"C2_T8","2,8":"C2_T9","4,16":"C2_T10","8,32":"C2_T11","2,8":"C2_T12","4,16":"C2_T13","8,32":"C2_T14","2,8":"C2_T15","4,16":"C2_T16","8,32":"C2_T17","2,8":"C2_T18","4,16":"C2_T19","8,32":"C2_T20","2,1":"C2_T21","4,32":"C2_T22","2,16":"C2_23","4,32":"C2_T24","2,16":"C2_T25","4,32":"C2_T26","2,16":"C2_T27","4,32":"C2_T28"}
        aurora={"2,4":["C2_T1","C2_T7"],"2,8":["C2_T2","C2_T8","C2_T9","C2_T12","C2_T15","C2_T18"],"2,16":["C2_T3","C2_T23","C2_T25","C2_T27"],"2,1":["C2_T4"],"2,2":["C2_T5","C2_T6"],"4,16":["C2_T10","C2_T13","C2_T19","C2_T16"],"8,32":["C2_T11","C2_T14","C2_T17","C2_T20"],"2,1":["C2_T21"],"4,32":["C2_T22","C2_T24","C2_T26","C2_T28"]}
        awslambda={"1,0.5":"C4_T1","2,1":"C4_T2","3,1":"C4_T3","4,2":"C4_T4","5,3":"C4_T5","6,3":"C4_T6","7,4":"C4_T7","8,4":"C4_T8","9,5":"C4_T9","10,6":"C4_T10"}
        dicti={}
        for i in range(self.layoutft.count()):
          item = self.layoutft.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layoutft.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layoutft.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        elasticachevalid=[]
        ec2valid=[]
        lambdavalid=[]
        auroravalid=[]
        for serv in ["ElastiCache","Elastic Kubernetes Service","Lambda","Aurora"]:
          mincpu=0
          maxcpu=0
          minmem=0
          maxmem=0
          em=""
          bm25=""
          faiss=""
          rm=""
          llama=""
          batch=""
          for k,v in dicti.items():
            if "Embedding model (GTE Large)" in k:
              if v!="All":
                em=[v]
              else:
                 em=["A100", "Inferentia","V100"]
            if "BM25" in k:
              if v!="All":
                bm25=[v]
              else:
                bm25=["A100", "CPU","V100"]
            if "FAISS" in k:
              if v!="All":
                faiss=[v]
              else:
                faiss=["A100", "CPU"]
            if "Reranking model" in k:
              if v!="All":
                rm=[v]     
              else:
                rm=["2-A100", "Inferentia","V100", "A100"]      
            if  "LLM LLaMA3-8B" in k:
              if v!="All":
                llama=[v]
              else:
                llama=["2-A100", "AWS Bedrock","Sambanova", "Cerebras","V100","A100","Inferentia"]
            if "Batch Size" in k:
              if v!="All":
                batch=[v]
              else:
                batch=[1,4]
            if serv in k and "Min" in k and "CPU" in k:
              mincpu=v
            if serv in k and "Max" in k and "CPU" in k:
              maxcpu=v
            if serv in k and "Min" in k and "Memory" in k:
              minmem=v
            if serv in k and "Max" in k and "Memory" in k:
              maxmem=v
          if "ElastiCache" in serv:
              cores=["1","2","4"]
              mems=["0.5","1","1.37","2","2.79","4","8","15.25","16","30.5","32"]
          if "Elastic Kubernetes Service" in serv:
              cores=["1","2","4","8","16"]
              mems=["1","2","4","8","16","32","64"]
          if "Lambda" in serv:
              cores=["0.5","1","2","3","4","5","6"]
              mems=["1","2","3","4","5","6","7","8","9","10"]
          if "Aurora" in serv:
              cores=["2","4","8"]
              mems=["1","2","4","8","16","32"]
          print(mincpu)
          print(maxcpu)
          print(cores)
          if mincpu in cores and maxcpu in cores:
            start_index = cores.index(mincpu)
            end_index = cores.index(maxcpu) + 1
            if start_index < end_index:
                valcores= cores[start_index:end_index]
          if minmem in mems and maxmem in mems:
            start_index = mems.index(minmem)
            end_index = mems.index(maxmem) + 1
            if start_index < end_index:
                valmems= mems[start_index:end_index]
          for valc in valcores:
            for valm in valmems:
              if "ElastiCache" in serv:
                  if valc+","+valm in elasticache.keys():
                      elasticachevalid.append(elasticache[valc+","+valm])
              if "Elastic Kubernetes Service" in serv:
                  
                  #print(ec2[valc+","+valm])
                  if valc+","+valm in ec2_10.keys():  
                      ec2valid.append(ec2_10[valc+","+valm])
                  if valc+","+valm in ec2_5.keys():                      
                      ec2valid.append(ec2_5[valc+","+valm])
                  if valc+","+valm in ec2_l.keys():                      
                      ec2valid.append(ec2_l[valc+","+valm])
              if "Lambda" in serv:
                  #print(valc+","+valm)
                  if valm+","+valc in awslambda.keys():
                      #print(valc+","+valm)
                      #print(awslambda[valc+","+valm])
                      lambdavalid.append(awslambda[valm+","+valc])
              if "Aurora" in serv:
                  print(valc+","+valm)
                  if valc+","+valm in aurora.keys():
                      print(valc+","+valm)
                      print(aurora[valc+","+valm])
                      auroravalid.extend(aurora[valc+","+valm])
        
        df = pd.read_excel("costs.xlsx", sheet_name="Retail+LLM")
        print(ec2valid)
        print(auroravalid)
        print(elasticachevalid)
        print(lambdavalid)
        print(df)
        print(df.columns)
        
        filtered_df = df[
            df['EKS Node Instance Type'].isin(ec2valid) &
            df['Mysql Instance Type'].isin(auroravalid) &
            df['Elasticache Instance Type'].isin(elasticachevalid) &
            df['Carts MS Lambda/EC2'].isin(lambdavalid) &
            df["Embedding model (GTE Large)"].isin(em) &
            df["BM25"].isin(bm25) &
            df["FAISS"].isin(faiss) &
            df["Reranking model (Mistral 7B)"].isin(rm) &
            df["LLM LLaMA3-8B"].isin(llama) &
            df["Batch Size"].isin(batch) 
        ]
        print(filtered_df)
        # Create and show the DataFrame viewer window
        self.viewer = DataFrameViewer(filtered_df, self)
        self.viewer.show()
    
    def select(self):
        print("came here...................")
        for item in self.items():
            if isinstance(item, Shape):
               item.proxy.setPos(item.pos().x(),item.pos().y())
            if isinstance(item, Arrow):
                t = 1/3
                x = item.end_item.x() + t * (item.start_item.x() - item.end_item.x())
                y = item.end_item.y() + t * (item.start_item.y() - item.end_item.y())

                item.proxy.setPos(QPointF(x, y).x(),QPointF(x, y).y())
            if isinstance(item, Shape) or isinstance(item, Arrow):
              if not item.proxy.isVisible():
                item.proxy.setVisible(True)
                item.checkbox.setVisible(True)
              else:
                item.proxy.setVisible(False)
                item.checkbox.setVisible(False)
        
        self.vpccheckboxes=[]
        print(self.vpcbuttons)
        for buttonnow in self.vpcbuttons:
          print(buttonnow)
          checkbox = QCheckBox(buttonnow)
          checkbox.move(5, buttonnow.height() // 2 - checkbox.height() // 2)  # 5 px from left, vertically centered
          checkbox.show()
          self.vpccheckboxes.append(checkbox)
            

    def filter_table(self):
        self.formGroupBoxft = QGroupBox()
        self.formGroupBoxft.setWindowTitle("Filter Params")
        self.formGroupBoxft.setWindowIcon(QtGui.QIcon('mapleicon.png'))
        self.layoutft = QFormLayout()

        self.c22 = QLabel("Please enter Filter Parameters:")
        self.c22.setAlignment(Qt.AlignCenter)
        self.layoutft.addRow(self.c22)
        
        currServices=[]
        for item in self.items():
              if isinstance(item, Shape):# and item.name not in currServices:
                  currServices.append(item.name)
        currServices.append("AWS Lambda")
        print(currServices)
        for serv in currServices:
            if serv != "Elastic Load Balancing" and serv != "User":
                
                cores=[]
                mems=[]
                if "ElastiCache" in serv:
                   cores=["1","2","4"]
                   mems=["0.5","1","1.37","2","2.79","4","8","15.25","16","30.5","32"]
                if "Elastic Kubernetes Service" in serv:
                   cores=["1","2","4","8","16"]
                   mems=["1","2","4","8","16","32","64"]
                if "Lambda" in serv:
                   cores=["0.5","1","2","3","4","5","6"]
                   mems=["1","2","3","4","5","6","7","8","9","10"]
                if "Aurora" in serv:
                   cores=["2","4","8"]
                   mems=["1","2","4","8","16","32"]
                if "EC2" not in serv:
                  self.label3 = QLabel(serv+" CPU Cores: Min: ")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(cores)
                  self.label3a = QLabel(serv+" CPU Cores: Max: ")
                  self.text_box1a = QComboBox()
                  self.text_box1a.addItems(cores)
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.layoutft.addRow(self.label3a,self.text_box1a)
                  self.label3 = QLabel(serv+" Memory: Min: ")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(mems)
                  self.label3a = QLabel(serv+" Memory: Max: ")
                  self.text_box1a = QComboBox()
                  self.text_box1a.addItems(mems)
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.layoutft.addRow(self.label3a,self.text_box1a)
                if "EC2" in serv:
                  self.label3 = QLabel("Embedding model (GTE Large):")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","A100", "Inferentia","V100"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("BM25:")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","A100", "CPU","V100"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("FAISS:")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","A100", "CPU"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("Reranking model (Mistral 7B):")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","2-A100", "Inferentia","V100", "A100"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  
                  self.label3 = QLabel("LLM LLaMA3-8B:")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","2-A100", "AWS Bedrock","Sambanova", "Cerebras","V100","A100","Inferentia"])
                  self.layoutft.addRow(self.label3,self.text_box1)
                  self.label3 = QLabel("Batch Size")
                  self.text_box1 = QComboBox()
                  self.text_box1.addItems(["All","1","4"])
                  self.layoutft.addRow(self.label3,self.text_box1)
        self.ok_button = QPushButton("Submit")
        self.cancel_button = QPushButton("Cancel")
        self.HBox = QHBoxLayout()
        self.HBox.addWidget(self.ok_button)
        self.HBox.addWidget(self.cancel_button)
        self.layoutft.addRow(self.HBox)
        self.ok_button.clicked.connect(self.open_table_viewer)
        self.cancel_button.clicked.connect(self.returner)
        self.formGroupBoxft.setLayout(self.layoutft)
        self.scrollArea = QScrollArea()
        self.scrollArea.setWidget(self.formGroupBoxft)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setFixedSize(500, 500)  # Adjust the size as needed

    # Show the QScrollArea
        self.scrollArea.show()
        #self.formGroupBoxft.show()

    def anotherform(self):
      self.formGroupBox3 = QGroupBox()
      self.formGroupBox3.setWindowTitle("Architecture Mappings")
      self.formGroupBox3.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layout3 = QFormLayout()

      self.c33 = QLabel("Please enter details to map the cloud services to the function it performs and also provide in which node of the EKS are their microservices located.")
      self.c33.setAlignment(Qt.AlignCenter)
      self.layout3.addRow(self.c33)
      #counter=0
      currServices=[]
      for item in self.items():
            if isinstance(item, Shape):# and item.name not in currServices:
                currServices.append(item.name)
      for serv in currServices:
          if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
              self.label3 = QLabel(serv+": ")
              self.text_box3 = QLineEdit("")
              self.layout3.addRow(self.label3,self.text_box3)
              #counter+=1
      for serv in currServices:
          if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
              self.label3 = QLabel(serv+" EKS Node No.: ")
              self.text_box3 = QLineEdit("")
              self.layout3.addRow(self.label3,self.text_box3)
              #counter+=1
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.anotherform2)
      cancel_button.clicked.connect(self.returner)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layout3.addRow(HBox)
      self.formGroupBox3.setLayout(self.layout3)
      self.formGroupBox3.show()

    def anotherform2(self):
      dicti={}
      #import Diagram
      currServices=[]
      for item in self.items():
            if isinstance(item, Shape):# and item.name not in currServices:
                currServices.append(item.name)
      for i in range(self.layout3.count()):
        item = self.layout3.itemAt(i)
        if item and isinstance(item.widget(),QLineEdit):
          val = item.widget().text()
          key = self.layout3.labelForField(item.widget()).text()
          dicti[key]=val
        elif item and isinstance(item.widget(),QComboBox):
          key = self.layout3.labelForField(item.widget()).text()
          val = item.widget().currentText()
          dicti[key]=val
      for k,v in dicti.items():
        for serv in currServices:
          if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
            if serv+":" in k:
              self.mappings[serv]=v
              GraphicView.classmapping[serv]=v
            if serv+" EKS Node No.:" in k:
              self.mappings[serv+" EKS Node"]=v
              GraphicView.classmapping[serv+" EKS Node"]=v
      self.formGroupBox3.close()
      self.anotherform3()
    #def anotherform4()
    def anotherform3(self):
        self.formGroupBoxc2 = QGroupBox() 
        self.formGroupBoxc2.setMaximumSize(800, 350)  # Adjusted maximum size for a better fit
        self.formGroupBoxc2.setMinimumSize(200, 100)  # Set a minimum size to ensure content fits
        #binary_group = QButtonGroup(self) 
        self.radio_widgetservs = QWidget(self) 
        radio_layout = QVBoxLayout(self.radio_widgetservs)
        question_layout = QVBoxLayout()
        heading = QLabel("Please select the services for which you want the latency and cost:")

        question_layout.addWidget(heading) 
        wk=[]
        for k,v in self.mappings.items():
          if "EKS" not in k:
            wk.append(k+":"+v)
        numvpc=len(self.vpclist)
        print(numvpc)
        print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
        for p in range(numvpc):
           wk.append("VPC "+str(p+1))
        #for serv in currServices:
        #    if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
        checkboxes_widget = QWidget(self)
        checkbox_layout = QVBoxLayout(checkboxes_widget)

        # Create "Select All" checkbox
        select_all_checkbox = QCheckBox("Select All")
        checkbox_layout.addWidget(select_all_checkbox)

        # Create individual checkboxes for each service
        service_checkboxes = []
        for serv in wk:
            #if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
            service_checkbox = QCheckBox(serv)
            service_checkboxes.append(service_checkbox)  # Store checkbox in dictionary
            checkbox_layout.addWidget(service_checkbox)

        question_layout.addWidget(checkboxes_widget)

        # Connect "Select All" checkbox to toggle other checkboxes
        select_all_checkbox.toggled.connect(lambda checked: self.toggle_all(checked, service_checkboxes))

        
        # Create buttons
        ok_button = QPushButton("Okay")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.setserv)
        cancel_button.clicked.connect(self.returner)

        # Horizontal layout for buttons
        HBox = QHBoxLayout()
        HBox.addWidget(ok_button)
        HBox.addWidget(cancel_button)
        h_layout_widget = QWidget()
        h_layout_widget.setLayout(HBox)

        question_layout.addWidget(h_layout_widget)

        # Set spacing and margins for the layout to make it compact
        question_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        question_layout.setSpacing(5)  # Reduce spacing

        self.formGroupBoxc2.setLayout(question_layout)
        self.formGroupBoxc2.show()


    def anotherform4(self):
      self.formGroupBoxc2 = QGroupBox()
      question_layout = QVBoxLayout()
      #if type == "Monolith":
      heading = QLabel("Please select the services for whom you want the latency to be shown. Please make sure the services you select create a single path. \nThe network latency won't be added otherwise. \nIf you select User, then the cost of user will not be calculated, obviously,. But the netweork cost from and to the user will be added.\nThe order of services are in the order of the arrows.")
      
      question_layout.addWidget(heading) 
      wk=[]
      # Collect services based on the provided diagram
      for item in self.items():
          if isinstance(item, Shape):# and item.name not in currServices:
              wk.append(item.name)
      checkboxes_widget = QWidget(self)
      checkbox_layout = QVBoxLayout(checkboxes_widget)

      # Create "Select All" checkbox
      select_all_checkbox = QCheckBox("Select All")
      checkbox_layout.addWidget(select_all_checkbox)
      numvpc=len(self.vpclist)
      print(numvpc)
      print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
      for p in range(numvpc):
          wk.append("VPC "+str(p+1))
      question_layout.addWidget(checkboxes_widget)
      # Create individual checkboxes for each service
      service_checkboxes = []
      for serv in wk:
          #if serv != "Amazon Elastic Kubernetes Services" and serv != "Elastic Load Balancing" and serv != "User":
          service_checkbox = QCheckBox(serv)
          service_checkboxes.append(service_checkbox)  # Store checkbox in dictionary
          checkbox_layout.addWidget(service_checkbox)

      

      # Connect "Select All" checkbox to toggle other checkboxes
      select_all_checkbox.toggled.connect(lambda checked: self.toggle_all(checked, service_checkboxes))

      # Create buttons
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.setserv)
      cancel_button.clicked.connect(self.returner)

      # Horizontal layout for buttons
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      h_layout_widget = QWidget()
      h_layout_widget.setLayout(HBox)

      question_layout.addWidget(h_layout_widget)

      # Set spacing and margins for the layout to make it compact
      question_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
      question_layout.setSpacing(5)  # Reduce spacing

      self.formGroupBoxc2.setLayout(question_layout)
      self.formGroupBoxc2.show()

    def takevpcinput(self):
      self.formGroupBoxvpc = QGroupBox()
      self.formGroupBoxvpc.setWindowTitle("VPC params")
      self.formGroupBoxvpc.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layoutvpc = QFormLayout()

      self.c11 = QLabel("Please enter your VPC Parameters:")
      self.c11.setAlignment(Qt.AlignCenter)
      self.layoutvpc.addRow(self.c11)
      self.label1 = QLabel("No. of connections: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Hours per day: ")
      self.text_box1 = QLineEdit("24")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("No. of NAT Gateways: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Data processed per NAT Gateway(GB/month): ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("No. of In-use public IPV4 addresses: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("No. of Idle public IPV4 addresses: ")
      self.text_box1 = QLineEdit("1")
      self.layoutvpc.addRow(self.label1,self.text_box1)
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.fillvpcdetails)
      cancel_button.clicked.connect(self.returnervpc)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layoutvpc.addRow(HBox)
      self.formGroupBoxvpc.setLayout(self.layoutvpc)
      self.formGroupBoxvpc.show()

    def returnervpc(self):
      self.formGroupBoxvpc.close()

    def fillvpcdetails(self):
      self.formGroupBoxvpc.close()
      dicti={}
      for i in range(self.layoutvpc.count()):
        item = self.layoutvpc.itemAt(i)
        if item and isinstance(item.widget(),QLineEdit):
          val = item.widget().text()
          key = self.layoutvpc.labelForField(item.widget()).text()
          dicti[key]=val
        elif item and isinstance(item.widget(),QComboBox):
          key = self.layoutvpc.labelForField(item.widget()).text()
          val = item.widget().currentText()
          dicti[key]=val
      connections=0
      hours=0
      for k,v in dicti.items():
        if "No. of connections:" in k:
          connections=int(v)
        elif "Hours per day:" in k:
          hours=int(v)
        elif "No. of NAT Gateways:" in k:
          nats=int(v)
        elif "Data processed per NAT Gateway(GB/month):" in k:
          natdata=float(v)
        elif "No. of In-use public IPV4 addresses:" in k:
          inuse=float(v)
        elif "No. of Idle public IPV4 addresses:" in k:
          idle=float(v)
      cost=((hours*730)/24)*connections*0.05
      cost+=nats*(32.85+(natdata*0.045))
      cost+=(3.65*int(inuse))+(3.65*int(idle))
      self.vpclist[int(self.vpcval)]={"connections":connections,"hours":hours,"NAT Gateways":nats,"Data processed per NAT Gateway":str(natdata)+" GB/month","No. of In-use public IPV4 addresses":inuse,"No. of Idle public IPV4 addresses":idle,"COST":str(cost)+" USD"}
      self.showvpc()


    def showvpc(self):
       msg = QMessageBox()
       msg.setWindowTitle("VPC Details")
       vpcdict={}
       vpcstr=""
       for k,v in self.vpclist[int(self.vpcval)].items():
          vpcdict[k]=v
          vpcstr+=str(k)+": "+str(v)+"\n\n"
       msg.setText(vpcstr)
       msg.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
       buttons = msg.buttons()
       for button in buttons:
          if button.text() == "OK":
              button.setText("Edit")  # Change the text of the OK button
          if button.text() == "Cancel":
              button.setText("OK")
       returnValue = msg.exec()
       if returnValue == QMessageBox.Ok:
          self.takevpcinput()
         

    def getvpcdetails(self,button):
       print("vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv")
       
       self.vpcval = button.text().split(" ")[-1]
       #self.vpcbuttons.append(button)
       #print(vpcval)
       vpccount=CSWidgets.vpccount
       if int(self.vpcval) not in list(self.vpclist.keys()):
          self.takevpcinput()
       else:
          self.showvpc()

    def setserv2(self,type):
        items = self.scene.items()
        self.selectedservs=[]
        arrc=0
        arrl=0
        for item in items:
            if isinstance(item,Shape) or isinstance(item,Arrow):
               item.proxy.setVisible(False)
            if isinstance(item,Shape)  and item.checkbox.isChecked():
               self.selectedservs.append(item.name)
               item.checkbox.setChecked(False)
            if isinstance(item,Arrow) and item.checkbox.isChecked():
               arrl+=item.Latency
               arrc+=item.Cost
               item.checkbox.setChecked(False)
        counter=1
        for checkboxnow in self.vpccheckboxes:
           print("vpclist.................")
           
           print(self.vpclist)
           if checkboxnow.isChecked() and counter in self.vpclist:
              print(self.vpclist)
              arrc+=float(self.vpclist[counter]["COST"].split()[0])
           counter+=1
           checkboxnow.setVisible(False)
        self.calcLat(self.selectedservs,type,arrl,arrc)
               

    def setserv(self):
      checkboxes = self.formGroupBoxc2.findChildren(QCheckBox)
      checked_checkboxes = [checkbox.text() for checkbox in checkboxes if checkbox.isChecked()]
      if checked_checkboxes is not None: 
          answer = checked_checkboxes
      else: 
          answer = []
          #count+=1 
      print(answer)
      if len(answer)>0:
         self.selectedservs=[]
         for ans in answer:
            if ":" in ans:
              servans=ans.split(":")[0].lower().split(" ")[1:]
              self.selectedservs.append(" ".join(servans).strip())
            else:
              self.selectedservs.append(ans)
      print("sssssssssssssssssssssssssssmkefmkfennnnnnnnnkefnkwemnr")
      print(self.selectedservs)
      self.calcLat(self.selectedservs,self.archtype)
      self.formGroupBoxc2.close()

    def finallat(self):
      dicti={}
      for i in range(self.layout2.count()):
        item = self.layout2.itemAt(i)
        if item and isinstance(item.widget(),QLineEdit):
          val = item.widget().text()
          key = self.layout2.labelForField(item.widget()).text()
          dicti[key]=val
        elif item and isinstance(item.widget(),QComboBox):
          key = self.layout2.labelForField(item.widget()).text()
          val = item.widget().currentText()
          dicti[key]=val
      inputdict={}
      print("dicttttttttttttttttttttttttttttttttttttttttiiiiiiiiii")
      print(dicti)
      for k,v in dicti.items():
        if "Concurrency:" in k:
          inputdict["workload"]=int(v)
        elif "MySQL Instance Type:" in k:
          inputdict["mysql_instance_type"]=v
        elif "Elasticache Instance Type:" in k:
          inputdict["elasticache_instance_type"]=v  
        elif "EKS Num Nodes:" in k:
          inputdict["eks_num_nodes"]=int(v)  
        elif "EKS Instance Type:" in k:
          inputdict["eks_instance_type"]=v 
        elif "Postgresql Instance Type:" in k:
          inputdict["postgresql_instance_type"]=v 
        elif "MQ Instance Type:" in k:
          inputdict["mq_instance_type"]=v 
      #if self.selectedservs[0].isnumeric():
      inputdict["services_list"]=self.selectedservs
      self.formGroupBox2.close()
      print(inputdict)
      outy=compute_eks_latency_cost(inputdict)
      tcost=0
      for vpc in self.selectedservs:
           if "VPC" in vpc:
              tcost+=float(self.vpclist[int(vpc.split(" ")[-1])]["cost"].split(" ")[0])

      CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(outy["latency"]))+" sec")
      CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(outy["cost"]+tcost))+" $")



    def calcCostLatency(self):
        tcost = 0.0
        #tlate = 0.0
        items = self.scene.items()
        for item in items:
            if isinstance(item,Shape):
                print(item.name)
                print(item.Cost)
                tcost += item.Cost
                #tlate += item.Latency
        #print(GraphicView.netcostlat_matrix)
        for pair in self.adjacency_matrix:
           print("XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
           print(pair[2].Cost)
           print(pair[2].Latency)
           tcost+=pair[2].Cost
           #tlate+=pair[2].Latency
                   

        CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tcost))+" $")
        #CSWidgets.totLateDisp.setText(" :: "+'<b><font color="red">{}</font></b>'.format("{:.4f}".format(tlate))+" sec")
        #CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(tlate))+" sec")

    


    def calcCloudCost(self): #cost for cloud for all item in scene
        totalsum = 0
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                    totalsum += item.model.CloudCost
                    #print(totalsum,"-",item.model.CloudCost)
        return totalsum

    def calcCloudLatency(self): 
        totalsum = 0
        items = self.scene.items()
        for item in items:
            if(item.type() == QGraphicsRectItem().type()):
                    totalsum += item.model.CloudLatency
                    print(totalsum,"-",item.model.CloudLatency,"-",item.CloudLatency)
        return totalsum/2
    
    def updateConfigList(self,config):
        self.configs.append(config)
        for item in self.scene.items():
            if(item.type() == QGraphicsRectItem().type()):
                item.configs = self.configs

    def updateTfrList(self,tfr):
        self.tfrs.append(tfr)
        for item in self.scene.items():
            if(item.type() == QGraphicsEllipseItem().type()):
                item.tfrs = self.tfrs
    
    def removeTfrFromList(self,tfr):
        self.tfrs.remove(tfr)
        for item in self.scene.items():
            if(item.type() == QGraphicsEllipseItem().type()):
                item.tfrs = self.tfrs

    def getNetLatency(self):
        def longestPath(graph):
            ans = 0
            n = len(graph)
            table = [-1] * n

            def dfs(u):
                if table[u] != -1:
                    return table[u]
                p_len = 0
                for v in graph[u]:
                    p_len = max(p_len, v[1] + dfs(v[0]))
                table[u] = p_len
                return p_len

            for i in range(n):
                ans = max(ans, dfs(i))    #graph[u] contains arrays of outgoing nodes; graph is an adjacency list
            return ans

        source = Shape("",10,10,0,0,"") #virtual node connected to all sources
        nodelist = [source]
        items = self.scene.items()
        for item in items:
            if((item.type() == QGraphicsRectItem().type()) | (item.type() == QGraphicsEllipseItem().type())):
                nodelist.append(item)
                #print('Appended : ',item.shape)
                if((item.in_arrows == []) and (item is not source)):
                    Arrow(source,item)

        graph = [[(nodelist.index(arrow.end_item),arrow.end_item.Latency) if arrow.end_item.type() == QGraphicsRectItem().type() or arrow.end_item.type() == QGraphicsEllipseItem().type() else (nodelist.index(arrow.end_item),0) for arrow in node.out_arrows] for node in nodelist]
        #print(graph)
        for arrow in source.out_arrows:
            arrow.end_item.in_arrows = []
        source.out_arrows = []
        return longestPath(graph)

    def getNetLatency2(self):
        source = Shape("",10,10,0,0,"") #virtual node connected to all sources
        nodelist = [source]
        items = self.scene.items()
        print('items lengh ',len(items))
        print(items)
        for item in items:
            if((item.type() == QGraphicsRectItem().type()) | (item.type() == QGraphicsEllipseItem().type())):
                nodelist.append(item)
                print('Appended : ',item.Latency,' - ',item.type())
                if((item.in_arrows == []) and (item is not source)):
                    Arrow(source,item)
        
        lat = 0
        for node in nodelist:
            max = 0
            for arrow in node.out_arrows:
                if(max < arrow.end_item.Latency):
                    max = arrow.end_item.Latency
            #print(max)
        #print('-')
        lat = lat + max
        
        return lat
        
    def getNetCloudLatency(self):
        def longestCloudPath(graph):
            ans = 0
            n = len(graph)
            table = [-1] * n

            def dfsCloud(u):
                if table[u] != -1:
                    return table[u]
                p_len = 0
                for v in graph[u]:
                    p_len = max(p_len, v[1] + dfsCloud(v[0]))
                table[u] = p_len
                return p_len

            for i in range(n):
                ans = max(ans, dfsCloud(i))    #graph[u] contains arrays of outgoing nodes; graph is an adjacency list
            return ans

        source = Shape("",10,10,0,0,"") #virtual node connected to all sources
        nodelist = [source]
        items = self.scene.items()
        #print('items', len(items))
        for item in items:
            if((item.type() == QGraphicsRectItem().type()) | (item.type() == QGraphicsEllipseItem().type())):
                nodelist.append(item)
                #print('Appended : ',item.shape)
                if((item.in_arrows == []) and (item is not source)):
                    Arrow(source,item)

        graph = [[(nodelist.index(arrow.end_item),arrow.end_item.CloudLatency) if arrow.end_item.type() == QGraphicsRectItem().type() or arrow.end_item.type() == QGraphicsEllipseItem().type() else (nodelist.index(arrow.end_item),0) for arrow in node.out_arrows] for node in nodelist]
        #print(graph)
        for arrow in source.out_arrows:
            arrow.end_item.in_arrows = []
        source.out_arrows = []
        return longestCloudPath(graph)  

    def saveCanvas(self):
      self.formGroupBox2 = QGroupBox()
      self.formGroupBox2.setWindowTitle("Architechture Type")
      self.formGroupBox2.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layout2 = QFormLayout()

      self.c22 = QLabel("Is this architechture a Microservice Architechture:")
      self.c22.setAlignment(Qt.AlignCenter)
      self.layout2.addRow(self.c22)
      ok_button = QPushButton("Yes")
      cancel_button = QPushButton("No")
      ok_button.clicked.connect(self.microservices)
      cancel_button.clicked.connect(self.monolith)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layout2.addRow(HBox)
      self.formGroupBox2.setLayout(self.layout2)
      self.formGroupBox2.show()

    def microservices(self):
       self.archtype="Microservices"
       self.formGroupBox2.close()
       self.saveCanvas2()

    def monolith(self):
       self.archtype="Monolith"
       self.formGroupBox2.close()
       self.saveCanvas2()


    #def saveCanvas(self):
    #    canvas_data = {'items' : []}
    #    item_data = None
    #    self.querytype()

    def saveCanvas2(self):
        canvas_data = {'items' : []}
        item_data = None
        #self.querytype()
        for item in self.scene.items():
            if isinstance(item,Shape):
                print("Shape Item")
                item_data ={
                    'object' : item,
                    'pos' : (item.x(),item.y()),
                #    'a' : item.a,
                #    'b' : item.b,
                   'cost' : item.Cost,
                   'latency' : item.Latency,
                #    'data' : item.RowNum,
                    'attributes' : item.attributes
                }
            # # print(item_data)
                canvas_data["items"].append(item_data)
                # print(item_data['attributes'])
        currmat=[]
        for pair in self.adjacency_matrix:
           currcost=pair[2].Cost
           currLat=pair[2].Latency
           curratt=pair[2].attributes
           currmat.append((pair[0],pair[1],(currcost,currLat,curratt)))
        canvas_data['adjacency_matrix'] = currmat
        print("")
        canvas_data["arch_type"]=self.archtype
        canvas_data["mapping"]=self.mappings
        #canvas_data["vpcbuttons"]=self.vpcbuttons
        squareservs={}
        for k,v in self.vpcsquares.items():
           servs=[]
           for s in v:
              servs.append(s[0])
           squareservs[k]=servs
        canvas_data["vpcsquares"]=squareservs
        canvas_data["vpclist"]=self.vpclist
        print(canvas_data)
        #for pair in self.adjacency_matrix:
        #   print(pair[2].Cost)
        #canvas_data['netattributes'] = GraphicView.netattributes
        #canvas_data['netcostlat_matrix'] = GraphicView.netcostlat_matrix
        # print(self.adjacency_matrix)
            # elif isinstance(item,Arrow):
            #     # item.start_item
            #     print('Arrow item')

            #     if item.start_item.pos1().x()<item.end_item.pos1().x():
            #         if item.start_item.pos1().y()<item.end_item.pos1().y():
            #             start = item.start_item.getBottomEdgePosition()
            #             end = item.end_item.getLeftEdgePosition()
            #         else:
            #             start = item.start_item.getTopEdgePosition()
            #             end = item.end_item.getLeftEdgePosition()
            #     else:
            #         if item.start_item.pos1().y()<item.end_item.pos1().y():
            #             start = item.start_item.getBottomEdgePosition()
            #             end = item.end_item.getLeftEdgePosition()
            #         else:
            #             start = item.start_item.getTopEdgePosition()
            #             end = item.end_item.getRightEdgePosition()


            #     item_data = {
            #         'type' : type(item).__name__,
            #         'start_pos': (start.x(),start.y()),
            #         'end_pos' : (end.x(),end.y()),
            #         'isHeaded' : item.isHeaded
            #     }
            #     print("Items::",item_data['start_pos'],item_data['end_pos'])
            #     canvas_data["items"].append(item_data)
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        fileName, _ = QFileDialog.getSaveFileName(self,"Save Logic","","Pickle Files (*.pkl)", options=options)
        with open(fileName,'wb') as file:
            pickle.dump(canvas_data,file)
            print("Diagram Saved")

    def loadTemplates(self):
        qm = QMessageBox()
        
        ret = qm.question(self,'Load Templates', "Are you sure you want to load Templates of "+CSWidgets.combobox1.currentText()+" domain?", qm.Yes | qm.No)
        if ret == qm.Yes:
            options = QFileDialog.Options()
            options |= QFileDialog.DontUseNativeDialog
            fileName, _ = QFileDialog.getOpenFileName(self,"Load Templates", "./templates/"+CSWidgets.combobox1.currentText()+"/","Pickle Files (*.pkl)", options=options)

            with open(fileName,'rb') as file:
                canvas_data = pickle.load(file)
            self.scene.clear()
            self.adjacency_matrix=[]
            CSWidgets.totLateDisp.setText("-- sec")
            #GraphicView.netattributes=[]
            #GraphicView.netcostlat_matrix=[]
            for item_data in canvas_data['items']:
                shapeObject = item_data['object']
                shapeObject.setPos(*item_data['pos'])
                # print(item_data['attributes'])
                shapeObject.attributes = item_data['attributes']
                self.scene.addItem(shapeObject)
                text_item=QGraphicsTextItem(shapeObject.name.replace("Amazon ",""))
                text_item.setDefaultTextColor(QColor(Qt.black))
                shapeObject.textItem = text_item
                # print(shapeObject.textItem)
                text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                text_item.setPos(QPointF(item_data['pos'][0],item_data['pos'][1]+55))
                self.scene.addItem(text_item)

            for pair in canvas_data['adjacency_matrix']:
                pair[0].setSelected(True)
                pair[1].setSelected(True)
                if len(pair)==3:
                  self.addArrow(pair[2])
                else:
                   self.addArrow(None)
                pair[0].setSelected(False)
                pair[1].setSelected(False)

            CSWidgets.button5.setEnabled(True)
            CSWidgets.buttonsave.setEnabled(True)
            CSWidgets.buttonClear.setEnabled(True)
            CSWidgets.costlatencybutton.setEnabled(True)
            #CSWidgets.deploybutton.setEnabled(True)
            CSWidgets.modelsBox.setEnabled(True)

    def loadCanvas(self):
        try:
            options = QFileDialog.Options()
            options |= QFileDialog.DontUseNativeDialog
            fileName, _ = QFileDialog.getOpenFileName(self,"Load Logic", ".","Pickle Files (*.pkl)", options=options)

            with open(fileName,'rb') as file:
                canvas_data = pickle.load(file)
            self.scene.clear()
            self.adjacency_matrix=[]
            CSWidgets.totLateDisp.setText("-- sec")
            print(canvas_data)
            
            #GraphicView.netattributes=[]
            #GraphicView.netcostlat_matrix=[]
            # print(canvas_data['adjacency_matrix'])
            #self.adjacency_matrix=canvas_data['adjacency_matrix']
            for item_data in canvas_data['items']:
                # item_type = eval(item_data['type'])
                # class_name = item_type.__name__
                # # print(class_name)
                # if class_name == 'Shape':
                #     item = item_type(
                #                     item_data['text'],
                #                      item_data['a'],
                #                      item_data['b'],
                #                      item_data['cost'],
                #                      item_data['latency'],
                #                      item_data['data'])
                #     # item = item_type(item_data['ModelShape'])
                #     item.setPos(*item_data['pos'])
                #     item.attributes = item_data['attributes']
                #     # item.setBrush(QColor(item_data['color']))
                #     # item.setPen(QColor(item_data['pen_color']))
                    shapeObject = item_data['object']
                    shapeObject.setPos(*item_data['pos'])
                    # print(item_data['attributes'])
                    shapeObject.setPos(QPointF(item_data['pos'][0],item_data['pos'][1]))
                    #shapeObject.
                    shapeObject.attributes = item_data['attributes']
                    import random
                    shapeObject.Cost =  item_data['cost']
                    if shapeObject.name!="Amazon DynamoDB":
                      shapeObject.Latency = random.randint(1,10)/1000# item_data['latency']
                    else:
                      shapeObject.Latency = item_data['latency']
                    self.scene.addItem(shapeObject)
                    text_item=QGraphicsTextItem(shapeObject.name.replace("Amazon Simple Storage System (S3)","S3"))
                    text_item.setDefaultTextColor(QColor(Qt.black))
                    shapeObject.textItem = text_item
                    # print(shapeObject.textItem)
                    text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
                    text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
                    text_item.setPos(QPointF(item_data['pos'][0],item_data['pos'][1]+55))
                    font = QFont()
                    font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
                    text_item.setFont(font)
                    self.scene.addItem(text_item)
            for pair in canvas_data['adjacency_matrix']:
                pair[0].setSelected(True)
                pair[1].setSelected(True)
                if len(pair)==3:
                  print("+++++++++++++++++++++++++++++++++++++++++")
                  self.addArrow(pair[2])
                else:
                   self.addArrow(None)
                pair[0].setSelected(False)
                pair[1].setSelected(False)
                # if class_name == 'Arrow':
                #     print("start loading",item_data["start_pos"])
                #     print("end loading",item_data["end_pos"])
                #     shapeItem1 = Shape("CSP",0,0,0,0,1)
                #     shapeItem2 = Shape("CSP",0,0,0,0,1)
                #     # transformer = Shape("",0,0,0,0,"")
                #     # # item = item_type(item_data['ModelShape'])
                #     # arrow = QGraphicsLineItem(item_data['start_pos'][0],item_data['start_pos'][1],item_data['end_pos'][0],item_data['end_pos'][1])
                #     #------------------------------------------------------------------------
                #     if item_data['isHeaded']:
                #         if(item_data['start_pos'][0]<item_data['end_pos'][0]):
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #             shapeItem2.setPos(item_data['end_pos'][0],item_data['end_pos'][1])
                #         else:
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #             shapeItem2.setPos(item_data['end_pos'][0]+100,item_data['end_pos'][1])
                #     else:
                #         if(item_data['start_pos'][1]<item_data['end_pos'][1]):
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['start_pos'][1])
                #             shapeItem2.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #         else:
                #             shapeItem1.setPos(item_data['start_pos'][0],item_data['start_pos'][1])
                #             shapeItem2.setPos(item_data['start_pos'][0],item_data['end_pos'][1])
                #     #-------------------------------------------------------------------------
                #     # transformer.setPos(shapeItem1.pos1().x(),shapeItem2.pos1().y())
                #     # # item.setPos(*item_data['pos'])
                #     # self.addArrow(shapeItem1,shapeItem2)
                #     # item.setBrush(QColor(item_data['color']))
                #     # item.setPen(QColor(item_data['pen_color']))
                #     arrow = Arrow(shapeItem1,shapeItem2,item_data['isHeaded'])
                #     # arrow2 = Arrow(transformer,shapeItem2,True)
                #     self.scene.addItem(arrow)
            CSWidgets.button5.setEnabled(True) 
            CSWidgets.buttonsave.setEnabled(True)
            CSWidgets.buttonClear.setEnabled(True)
            CSWidgets.costlatencybutton.setEnabled(True)
            CSWidgets.selectbutton.setEnabled(True)
            #CSWidgets.deploybutton.setEnabled(True)
            CSWidgets.modelsBox.setEnabled(True)
            if "arch_type" in canvas_data:
              self.archtype=canvas_data["arch_type"]
            if "mappings" in canvas_data:
               self.mappings=canvas_data["mappings"]
            #if "vpcbuttons" in canvas_data:
            #  self.vpcbuttons=canvas_data["vpcbuttons"]
            #else:
            #self.vpcbuttons=[]
            if "vpcsquares" in canvas_data:
              tempvpcsquares=canvas_data["vpcsquares"]
              for k,v in tempvpcsquares.items():
                 self.drawsquare(v,int(k))

            if "vpclist" in canvas_data:
              self.vpclist=canvas_data["vpclist"]
            #csw=CSWidgets()
            #CSWidgets.addvpcbutton(self.vpcbuttons)
            vpccount=1
            #for button in self.vpcbuttons:
            #  CSWidgets.vpcdepbox.addWidget(button)
            for square in self.vpcsquares:
              #self.scene.addItem(square[1])
              optimizeArch = QPushButton("VPC "+str(vpccount))
              if vpccount==1:
                  optimizeArch.setStyleSheet("background-color: red;")
              elif vpccount==2:
                  optimizeArch.setStyleSheet("background-color: blue;")
              elif vpccount==3:
                  optimizeArch.setStyleSheet("background-color: green;")
              elif vpccount==4:
                  optimizeArch.setStyleSheet("background-color: pink;")
              optimizeArch.clicked.connect(lambda: self.getvpcdetails(optimizeArch))
              self.vpcbuttons.append(optimizeArch)
              
              #global vpcdepbox
              CSWidgets.vpcdepbox.addWidget(optimizeArch)
            #if self.archtype=="Microservices":
            #CSWidgets.totCostDisp.setText("3432.024 3432.024")
            #CSWidgets.totLateDisp.setText("7.047 sec")
            CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(7.047))+" sec")
            CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(3432.024))+" $/month")

            #else:
            #  self.calcCostLatency()
            #print("Adj Mat:\n"+str(self.adjacency_matrix)+"\nEnd of Adj Mat")
        except FileNotFoundError:
            print("Canvas Data File Not Found")

    def propagateWorkload(workLoadObject):
        self.nodes = [item for item in self.scene.items() if isinstance(item,Shape)]
        visited = set()
        queue = deque(self.nodes[0])
        while queue:
            currNode = queue.popleft()
            if currNode not in visited:
                currNode.workLoad = workLoadObject
                visited.add(currNode)
                for edge in self.adjacency_matrix:
                    if edge[0]==currNode and edge[1] not in visited:
                        queue.append(edge[1])
    def returner(self):
      self.formGroupBox1.close()
      return
    
    
    def deletesquares(self,services,number):
       servs=self.vpcsquares[number]
       #deletions=[]
       additions=[]
       print(services,number)
       print(servs)
       for s in servs:
          if s[0] not in services:
             self.scene.removeItem(s[1])
       for s in services:
          doadd=True
          for s1 in servs:
            if s==s1[0]:
               doadd=False
          if doadd:
            additions.append(s)
       self.drawsquare(additions,number)


    def drawsquare(self,services,number):
      if len(self.vpcsquares)<number:
        self.vpcsquares[number]=[]
      for item in self.scene.items():
          if isinstance(item,Shape) and item.name in services:
              

              # Set the border color and thickness
              if number==1:
                border_color = QColor(255, 0, 0)  # Red color
              elif number==2:
                border_color = QColor(0, 0, 255)
              elif number==3:
                border_color = QColor(0, 255, 0)
              elif number==4:
                border_color = QColor(255, 192, 203)
              border_thickness = 10

              # Set pen with color and thickness
                            # Draw the square
              from Vpcsquare import CustomWidget
              
              square_sizey = 95
              top_left_x = item.x()-10
              top_left_y = item.y()-15
              print("Text Item-------------------------------------------------------------"+item.textItem.toPlainText())
              text_rect = item.textItem.boundingRect()
              top_left_local = text_rect.topLeft()
              top_right_local = text_rect.topRight()
              bottom_left_local = text_rect.bottomLeft()
              bottom_right_local = text_rect.bottomRight()
              top_left_scene = item.textItem.mapToScene(top_left_local)
              top_right_scene = item.textItem.mapToScene(top_right_local)
              bottom_left_scene = item.textItem.mapToScene(bottom_left_local)
              bottom_right_scene = item.textItem.mapToScene(bottom_right_local)

              # Print the text and the corner coordinates
              # print(f"Text: {text}")
              print("Item x:",item.x())
              print(f"Top-left corner: {top_left_scene}")
              print(f"Top-right corner: {top_right_scene}")
              print(f"Bottom-left corner: {bottom_left_scene}")
              print(f"Bottom-right corner: {bottom_right_scene}")
              square_sizex = int(top_right_scene.x()+5-top_left_x)

              cw=CustomWidget(square_sizex,square_sizey,top_left_x,top_left_y,border_color,border_thickness)
              cw.setZValue(0)
              self.scene.addItem(cw)
              self.vpcsquares[number].append((item.name,cw))

              
      

    def getworkload(self):
      print("Hereeeee")
      self.formGroupBox1 = QGroupBox()
      self.formGroupBox1.setWindowTitle("Input Workload")
      self.formGroupBox1.setWindowIcon(QtGui.QIcon('mapleicon.png'))
      self.layout1 = QFormLayout()

      self.c11 = QLabel("Please enter your Workload Parameters:")
      self.c11.setAlignment(Qt.AlignCenter)
      self.layout1.addRow(self.c11)
      self.label1 = QLabel("Throughput: ")
      self.text_box1 = QLineEdit("1000")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Throughput unit (req/___): ")
      self.text_box1 = QComboBox()
      self.text_box1.addItems(["second","minute","hour","day","week","month","year"])
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Latency (sec): ")
      self.text_box1 = QLineEdit("0.2")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Memory (MB): ")
      self.text_box1 = QLineEdit("10240")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Target Service: ")
      currServices = []
      for item in self.scene.items():
         if isinstance(item,Shape) and item.name not in currServices:
            currServices.append(item.name)
      self.text_box1 = QComboBox()
      self.text_box1.addItems(currServices)
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Target Current Memory (MB): ")
      self.text_box1 = QLineEdit("10240")
      self.layout1.addRow(self.label1,self.text_box1)
      self.label1 = QLabel("Target Current CPU (Cores): ")
      self.text_box1 = QLineEdit("2")
      self.layout1.addRow(self.label1,self.text_box1)
      ok_button = QPushButton("Okay")
      cancel_button = QPushButton("Cancel")
      ok_button.clicked.connect(self.optimizeArchitecture)
      cancel_button.clicked.connect(self.returner)
      HBox = QHBoxLayout()
      HBox.addWidget(ok_button)
      HBox.addWidget(cancel_button)
      self.layout1.addRow(HBox)
      self.formGroupBox1.setLayout(self.layout1)
      self.formGroupBox1.show()

    def getresponse(self,query,outtokens):
            response = client.chat.completions.create(
                model=deployment_name,
                messages=[
                    { "role": "system", "content": "You are a AWS Cloud expert." },
                    
                    { "role": "user", "content": [  
                        { 
                            "type": "text", 
                            "text": query 
                        }
                        
                    ] } 
                ],
                
                max_tokens=outtokens
            )

            return(response.choices[0].message.content)

    def updatearch(self,newlatency,newcost,oldserv,newserv):
          newadjmat=[]
          newitems=[]
          newitemsposx=[]
          newitemsposy=[]
          newitemsposxy=[]
          #newitems=[]

          for pair in self.adjacency_matrix:
              newpair=pair.copy()
              if oldserv in pair[0].name and ((pair[0].x(),pair[0].y()) not in newitemsposxy):
                  newpair=pair.copy()
                  if oldserv!=newserv:
                    lambdashapecopy=pair[0].replaceByOpt(newserv,"CSP",newlatency,newcost)
                  else:
                    lambdashapecopy=pair[0]
                    lambdashapecopy.Latency=newlatency
                    lambdashapecopy.Cost=newcost
                  #lambdashapecopy=Shape("CSP",0,0,total_cost,max_latency,215)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsSelectable,True)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsMovable,True)
                  #lambdashapecopy.setPos(pair[0].x(),pair[0].y())
                  print(lambdashapecopy)
                  newpair[0]=lambdashapecopy
              elif oldserv in pair[0].name:
                  newpair[0]=newitems[newitemsposxy.index((pair[0].x(),pair[0].y()))]
                  
              if oldserv in pair[1].name and ((pair[1].x(),pair[1].y()) not in newitemsposxy):
                  print("prionting.....")
                  print(oldserv)
                  print(pair[0].name)
                  if oldserv!=newserv:
                    lambdashapecopy=pair[1].replaceByOpt(newserv,"CSP",newlatency,newcost)
                  else:
                    lambdashapecopy=pair[1]
                    lambdashapecopy.Latency=newlatency
                    lambdashapecopy.Cost=newcost
                  #lambdashapecopy=Shape("CSP",0,0,total_cost,max_latency,215)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsSelectable,True)
                  #lambdashapecopy.setFlag(QGraphicsEllipseItem.ItemIsMovable,True)
                  #lambdashapecopy.setPos(pair[1].x(),pair[1].y())
                  newpair[1]=lambdashapecopy
              elif oldserv in pair[1].name:
                  newpair[1]=newitems[newitemsposxy.index((pair[1].x(),pair[1].y()))]
                  
              newadjmat.append(newpair)
              print("prionting&&&&&&&&&&&&&&&&&&&")
              print(oldserv)
              print(pair[0].name)
              print(pair[1].name)
              print(pair)
              print(newpair)
              print(newpair[0].attributes)
              print(newpair[1].attributes)
              print("---------------------------------")
              if newpair[0] not in newitems and ((pair[0].x(),pair[0].y()) not in newitemsposxy):
                newitems.append(newpair[0])
                newitemsposx.append(newpair[0].x())
                newitemsposy.append(newpair[0].y())
                newitemsposxy.append((newpair[0].x(),newpair[0].y()))
              if newpair[1] not in newitems:
                newitems.append(newpair[1])
                newitemsposx.append(newpair[1].x())
                newitemsposy.append(newpair[1].y())
                newitemsposxy.append((newpair[1].x(),newpair[1].y()))
          self.adjacency_matrix=newadjmat
          #self.calcCostLatency()
    


    def insertarch(self,newlatency,newcost,oldserv,newserv,dir):
          #CSWidgets.totCostDisp.setText("1992.528 $/m")
          #CSWidgets.totCostDisp.setText("5.5037 sec")
          CSWidgets.totLateDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(5.5037))+" sec")
          CSWidgets.totCostDisp.setText('<b><font color="red">{}</font></b>'.format("{:.4f}".format(1992.528))+" $/month")

          newadjmat=[]
          newitems=[]
          newitemsposx=[]
          newitemsposy=[]
          newitemsposxy=[]
          #newitems=[]
          print("mmmmmmmmmmmmmmmmmmmmmmmmmmmmmm")
          #print(dir=="from")
          selflist=[]
          newservlist=[]
          for pair in self.adjacency_matrix:
              newpair=pair.copy()
              newpair2=pair.copy()
              print("kkkkkkkkkkkkkkkkkkkkk")
              #print(oldserv)
              print(pair[0].name)
              print((pair[0].x(),pair[0].y()))
              print(pair[1].name)
              print((pair[1].x(),pair[1].y()))
              print(newitemsposxy)
              #print(newitemsposxy)
              if (dir=="from" or dir=="both") and oldserv in pair[0].name and ((pair[0].x(),pair[0].y()) not in newitemsposxy):
                  print("oooooooooooooooooooooooooo")
                  lambdashapecopy=pair[0].addOpt(newserv,"CSP",newlatency,newcost,dir,pair[1])
                  selflist.append(pair[0])
                  newservlist.append(lambdashapecopy)
                  newpair[0]=lambdashapecopy
                  newpair2[1]=lambdashapecopy
              elif (dir=="from" or dir=="both") and oldserv in pair[0].name:
                  newpair[0]=newitems[newitemsposxy.index((pair[0].x(),pair[0].y()))]    
                  newpair2[1]=newitems[newitemsposxy.index((pair[0].x(),pair[0].y()))]    
              if (dir=="to" or dir=="both") and oldserv in pair[1].name and ((pair[1].x(),pair[1].y()) not in newitemsposxy):
                  lambdashapecopy=pair[1].addOpt(newserv,"CSP",newlatency,newcost,dir,pair[0])
                  newservlist.append(lambdashapecopy)
                  selflist.append(pair[1])
                  newpair[1]=lambdashapecopy
                  newpair2[0]=lambdashapecopy
              elif (dir=="to" or dir=="both") and oldserv in pair[1].name:
                  newpair[1]=newitems[newitemsposxy.index((pair[1].x(),pair[1].y()))]
                  newpair2[0]=newitems[newitemsposxy.index((pair[1].x(),pair[1].y()))]
              print("eeeeeeeeeeeeeeeeeeeeee")
              print(newpair[0].name)
              print(newpair[1].name)
              print(newpair2[0].name)
              print(newpair2[1].name)
              newadjmat.append(newpair)
              newadjmat.append(newpair2)
              if (dir=="from" or dir=="both") and (newpair[0].x(),newpair[0].y()) not in newitemsposxy:
                #newitems.append(pair[0])
                newitems.append(newpair[0])
                #newitemsposxy.append((pair[0].x(),pair[0].y()))
                newitemsposxy.append((newpair[0].x(),newpair[0].y()))
              elif (dir=="to" or dir=="both") and (newpair[1].x(),newpair[1].y()) not in newitemsposxy:
                #newitems.append(pair[1])
                newitems.append(newpair[1])
                #newitemsposxy.append((pair[1].x(),pair[1].y()))
                newitemsposxy.append((newpair[1].x(),newpair[1].y()))
          counter=0
          for selfs in selflist:
            self.scene.removeItem(selfs)
            for item in self.scene.items():
              if isinstance(item,QGraphicsTextItem) and item.toPlainText()==selfs.name:
                  #print(item)
                  self.scene.removeItem(item)
            ptX = selfs.x()
            ptY = selfs.y()
            selfs.setPos(QPointF(ptX-50, ptY+150))
            self.addObject(selfs)
            self.scene.addItem(selfs)
            text_item=QGraphicsTextItem(selfs.name.replace("Amazon",""))
            text_item.setDefaultTextColor(QColor(Qt.black))
            
            text_item.setFlag(QGraphicsTextItem.ItemIsSelectable,True)
            text_item.setFlag(QGraphicsTextItem.ItemIsMovable,True)
            text_item.setPos(QPointF(ptX-50,ptY+150+55))
            selfs.textItem = text_item
            font = QFont()
            font.setPointSize(15)  # Increase the font size to 16

                    # Apply the font to the QGraphicsTextItem
            text_item.setFont(font)
            self.scene.addItem(text_item)
            if (dir=="from" or dir=="both"):
              self.addArrowopt(selfs,newservlist[counter],False)
            if (dir=="to" or dir=="both"):
               self.addArrowopt(newservlist[counter],selfs,False)
            counter+=1
          self.adjacency_matrix=newadjmat
          
        

    def msgbox(self,oldcost,oldlat,oldmem,newcost,newlat,newmem,oldserv,newserv,throughput,waswithin,iswithin,op,direct): 
      msgBox = QMessageBox()
      msgBox.setIcon(QMessageBox.Information)
      print(direct)
      if not direct and (newserv!=oldserv or oldmem!=newmem):
        lat=f"Maximum Latency {newserv} with {newmem} MB (Makespan): {newlat:.2f} seconds"
        cost=f"Total Cost for {newserv} with {newmem} MB {throughput} invocations: ${newcost:.8f}"
        extra=lat+" :: "+cost
        latec2=f"Maximum Latency of {oldserv}  with {oldmem} MB (Makespan): {oldlat:.2f} seconds"
        costec2=f"Total Cost for {oldserv}  with {oldmem} MB {throughput} invocations: ${oldcost:.8f}"
        extra2=latec2+" :: "+costec2
        if newlat>oldlat:
           latsign="+"
        else:
           latsign="-"
        if newcost>oldcost:
           costsign="+"
        else:
           costsign="-"
        msgBox.setText(extra+"\n\n"+extra2+"\n\n\n"+"Recommendation : "+oldserv+" with "+oldmem+" MB to "+newserv+" with "+newmem+" MB,\n\nLatency: "+latsign+" "+str(abs((oldlat*100)-(newlat*100))/oldlat)+" %\n\n\n"+"SLA Violation of "+oldserv+" with "+oldmem+" MB: "+str(waswithin)+"\n\n\n"+"SLA Violation of "+newserv+" with "+newmem+" MB: "+str(iswithin)+"\n\n\n"+"Cost: "+costsign+" "+str(abs(newcost-oldcost)*100/oldcost)+" %\n\n\nDo you want to make the change?")
        msgBox.setWindowTitle("Optimized Results")
        msgBox.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        def msgButtonClick(i):
          print("Button clicked is:",i.text())
        msgBox.buttonClicked.connect(msgButtonClick)

        returnValue = msgBox.exec()
        if returnValue == QMessageBox.Ok:
          print('OK clicked')
          #if newserv==oldserv and oldmem!=newmem:
          #   self.calcCostLatency()
          if op=="replace":
            self.updatearch(newlat,newcost,oldserv,newserv)
          elif op=="insert":
            self.insertarch(newlat,newcost,oldserv,newserv,"both")
      elif not direct:
          msgBox.setText("The current architechture with "+oldserv+" is sufficient for your work.")
          msgBox.setWindowTitle("Optimized Results")
          msgBox.setStandardButtons(QMessageBox.Ok)
          def msgButtonClick(i):
            print("Button clicked is:",i.text())
          msgBox.buttonClicked.connect(msgButtonClick)
      
          returnValue = msgBox.exec()
          if returnValue == QMessageBox.Ok:
            print('OK clicked')
          print("The current architechture with AWS EC2 is sufficient for your work.")
      elif direct:
          msgBox.setText("Recomend: "+oldserv+" with "+oldmem+" MB to "+newserv+".")
          msgBox.setWindowTitle("Optimized Results")
          msgBox.setStandardButtons(QMessageBox.Ok)
          def msgButtonClick(i):
            print("Button clicked is:",i.text())
          msgBox.buttonClicked.connect(msgButtonClick)
      
          returnValue = msgBox.exec()
          if returnValue == QMessageBox.Ok:
            print('OK clicked')
         


    def optimizeArchitecture(self):
        #self.getworkload()   
        # exit(0)
        dicti={}
        for i in range(self.layout1.count()):
          item = self.layout1.itemAt(i)
          if item and isinstance(item.widget(),QLineEdit):
            val = item.widget().text()
            key = self.layout1.labelForField(item.widget()).text()
            dicti[key]=val
          elif item and isinstance(item.widget(),QComboBox):
            key = self.layout1.labelForField(item.widget()).text()
            val = item.widget().currentText()
            dicti[key]=val
        for k,v in dicti.items():
          if "Throughput:" in k:
            throughput=v
          elif "Latency" in k:
            latency=v
          elif "Service" in k:
            print("Target Value",v)
            target=v
          elif "Target Current Memory" in k:
            currentmemory=v
          elif "Memory (MB):" in k:
            memory=v
          elif "(req/___)" in k:
            unit=v
          elif "Cores" in k:
            cores=v
        self.formGroupBox1.close()
        
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(30, 40, 200, 25)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.show()
        #for i in range(101):
            # Here you would do some actual work, for example:
        #    time.sleep(0.1)
        self.progress_bar.setValue(1)
        #QApplication.processEvents()
        #throughput="1000"
        #latency="10"
        #memory="10200"
        ec2cores="4"
        oldlatency=0
        oldcost=0
        newlatency=0
        newcost=0
        #ec2mem="16384"
        lambdamemreq=str(int(memory))
        z=0
        zc=0
        from fastrag.stores import PLAIDDocumentStore
        from openai import AzureOpenAI
        from fastrag.retrievers.colbert import ColBERTRetriever
        
        print("optimize Arch called\n"+str(self.adjacency_matrix))
        services=[]
        for pair in self.adjacency_matrix:
            if pair[0] not in services:
                services.append(pair[0])
            if pair[1] not in services:
                services.append(pair[1])
        #print(services)
        self.progress_bar.setValue(2)
        #QApplication.processEvents()
        servicesnames=[]
        for serv in services:
            servicesnames.append(serv.name)
        print(servicesnames)
        self.progress_bar.setValue(10)
        #QApplication.processEvents()
        mappy={}
        l=10
        for serv in servicesnames:
            #self.progress_bar.setValue(l+10)
            #QApplication.processEvents()
            #l+=10
            if "User" in serv:
              continue
            if target not in serv:
              continue
            replacements=self.getresponse("List all AWS services that are possible replacements for the service "+serv+". The services you list should be similar to "+serv+" according to their functionality. Only output the AWS services names one by one seperated by newlines. Do not write any more text. Do not provide any explanation. Do not perform numbering or bulleting of the output.",100)
            print("servvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv")
            print(serv)
            #print(replacements)
            newservs=[serv]
            newservs.extend(replacements.split("\n"))
            docs=[]
            print(newservs)
            if "EC2" in serv and "AWS Lambda" not in newservs:
              newservs.append("AWS Lambda")
            if "S3" in serv and "Glacier" not in newservs:
               newservs.append("Amazon S3 Glacier")
            if "DynamoDB" in serv:
               newservs=["Amazon DynamoDB Accelerator (DAX)"]
            print(newservs)
            
            for se in newservs:
              print("oooooooooooooooooooooooooooooooooooooooooooooooooooooooo")
              print(se)
              
              retriever=None
              if "DynamoDB" in se:
                 store = PLAIDDocumentStore(index_path="chunkedtsv2/dynamo_index",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/dynamodb.tsv")

                 retriever = ColBERTRetriever(store)
              if "EC2" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/ec2-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/ec2-ug.tsv")

                retriever = ColBERTRetriever(store)
              if "Quicksight" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/amazon-quicksight-user.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/amazon-quicksight-user.tsv")

                retriever = ColBERTRetriever(store)
              elif "Athena" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/athena-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/athena-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Backup" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/AWSBackup-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/AWSBackup-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Snowball" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/AWSSnowball-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/AWSSnowball-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Brew" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/databrew-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/databrew-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "EBS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/ebs-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/ebs-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "ECS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/ecs-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/ecs-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "EFS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/efs-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/efs-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Firehose" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/firehose-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/firehose-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Glue" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/databrew-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/databrew-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Greengrass" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/greengrass-v2-developer-guide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/greengrass-v2-developer-guide.tsv")

                retriever = ColBERTRetriever(store)
              elif "Analytics" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iotanalytics-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iotanalytics-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Core" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iot-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iot-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Event" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iotevents-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iotevents-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Sitewise" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/iot-sitewise-guide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/iot-sitewise-guide.tsv")

                retriever = ColBERTRetriever(store)
              elif "Lambda" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/lambda-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/lambda-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Lightsail" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/lightsail-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/lightsail-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "RDS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/rds-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/rds-ug.tsv")

                retriever = ColBERTRetriever(store)
              elif "Redshift" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/redshift-gsg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/redshift-gsg.tsv")

                retriever = ColBERTRetriever(store)
              elif "S3" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/s3-userguide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/s3-userguide.tsv")

                retriever = ColBERTRetriever(store)
              elif "Sagemaker" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/sagemaker-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/sagemaker-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Service" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/service-guide.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/service-guide.tsv")

                retriever = ColBERTRetriever(store)
              elif "SNS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/sns-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/sns-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "SQS" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/sqs-dg.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/sqs-dg.tsv")

                retriever = ColBERTRetriever(store)
              elif "Storage" in se:
                store = PLAIDDocumentStore(index_path="chunkedtsv2/storagegateway-s3file-ug.tsv",
                           checkpoint_path="Colbertv2_NQ",
                           collection_path="chunked/storagegateway-s3file-ug.tsv")

                retriever = ColBERTRetriever(store)
              self.progress_bar.setValue(l+2)
              #QApplication.processEvents()
              print("tttttttttttttttttttttttttttttttttt")
              print(se)
              l+=10
              if retriever!=None:
                #res=None
                #if target in ["Amazon EC2","AWS Lambda","Amazon DynamoDB"]:
                res=retriever.run("I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per "+unit+" and memory of "+memory+" MB. Is "+serv+" with memory configuration "+str(currentmemory)+" and cores "+str(cores)+", sufficient for that workload? If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this?", 10)
                

                self.progress_bar.setValue(l+2)
                #QApplication.processEvents()
                l+=10
                import numpy as np
                from joblib import load
                import pandas as pd
                READ_COST_PER_1000 = 0.005
                WRITE_COST_PER_1000 = 0.005
                STORAGE_COST_PER_GB_MONTH = 0.023
                if res is not None:
                  for d in res["documents"]:
                    #print(d)
                    docs.append(d.content)
            
              print("seeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee")
              print(se)
              if "EC2" in se:
                print("doing EC2")
                z=(0.05625*int(throughput))-(0.05625*int(ec2cores))+0.45
                oldlatency=z
                zc=1.76
                oldcost=zc
                docs.append("The latency obtained for "+se+" is "+str(z)+" seconds and cost per hour is "+str(zc)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
              if "Lambda" in se:
                print("Manju Code starting...")
                dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(lambdamemreq), "function_defn":"DTS_deepreader","memory_required":int(memory)})
                max_latency=dictlambda["latency"]
                total_cost=dictlambda["cost"]
                newlatency=max_latency
                newcost=total_cost
                print("Manju Code done....")
                docs.append("The latency obtained for "+se+" of memory configuration "+str(lambdamemreq)+" MB, is "+str(max_latency)+" seconds and cost per hour is "+str(total_cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                if "Lambda" in serv:
                    dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(currentmemory), "function_defn":"DTS_deepreader","memory_required":int(memory)})
                    max_latency=dictlambda["latency"]
                    total_cost=dictlambda["cost"]
                    print("Manju Code done....")
                    oldlatency=max_latency
                    oldcost=total_cost
                    #newlatency=max_latency
                    #newcost=total_cost
                    docs.append("The latency obtained for "+se+" of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency)+" seconds and cost per hour is "+str(total_cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
              if "S3" in se and "S3" in serv:
                print("print hereeeee")
                dicts3 = compute_s3_latency_and_cost({"workload" : int(throughput), "memoryConfig" : int(currentmemory) , "fileSize" : 20480, "operation": "read"})
                cost=dicts3["cost"]
                max_latency_sec=dicts3["latency"]
                cost=0.49
                oldlatency=max_latency_sec
                oldcost=cost
                docs.append("The latency obtained for "+se+" of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency_sec)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                docs.append("The latency obtained for AWS S3 Glacier of memory configuration "+str(currentmemory)+" MB, is "+str(60)+" seconds and cost per hour is "+str(cost*0.35)+" USD, for the workload of throughput of "+throughput+" requests per second and memory of "+memory+" MB and latency requirement of "+latency+" secs.")
              print("kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk")
              print(se)
              if "DynamoDB" in se:
                print("juntaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")
                dictdynamo=compute_dynamodb_latency_and_cost({"workload":throughput,"data_size":100000,"chunk_size":10000})
                cost=dictdynamo["cost"]
                max_latency=dictdynamo["latency"]
                costdax=cost*2
                max_latency_dax=0.01
                docs.append("The latency obtained for "+se+" of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                docs.append("The latency obtained for Amazon DynamoDB Accelerator (DAX) of memory configuration "+str(currentmemory)+" MB, is "+str(max_latency_dax)+" seconds and cost per hour is "+str(costdax)+" USD, for the workload of throughput of "+throughput+" requests per second and memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                #newlatency=180
                #newcost=cost*0.35
              if "ElastiCache" in se:
                 
                 dictelasticache1=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.micro"})
                 dictelasticache2=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.small"})
                 dictelasticache3=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.micro"})
                 dictelasticache4=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.small"})
                 cost=dictelasticache1["cost"]
                 max_latency=dictelasticache1["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(1024)+" MB, and "+str(1)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                 cost=dictelasticache1["cost"]
                 max_latency=dictelasticache1["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(2048)+" MB, and "+str(1)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                 cost=dictelasticache3["cost"]
                 max_latency=dictelasticache3["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(1204)+" MB, and "+str(2)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")
                 cost=dictelasticache4["cost"]
                 max_latency=dictelasticache4["latency"]
                 docs.append("The latency obtained for "+se+" of memory configuration "+str(2048)+" MB, and "+str(2)+ " cores, is "+str(max_latency)+" seconds and cost per hour is "+str(cost)+" USD, for the workload of throughput of "+throughput+" requests per second, memory of "+memory+" MB and latency requirement of "+latency+" secs.")

            #print(docs)

            self.progress_bar.setValue(l+5)
              #QApplication.processEvents()
            l+=10
            if "ElastiCache" in target:
               print("elasticache")
               print(docs)
               while(True):
                pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? The memory configuration for "+serv+" is "+str(currentmemory)+" MB, and have "+str(cores)+ " cores. For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself, along with its configuration. Otherwise just state the name of the alternate service along with its configuration. Do not unnecessarily predict other services if the current is sufficient. So if the latency of the service is less than the workload latency, predict "+serv+" with its configuration. This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But definately provide the configuration of the service. For configuration provide memory and cores. Do not miss out on the configuration. Do not forget to mention cores. Exactly mention the memory and cores. Not just some vague configuration \nDocuments:\n"+"\n".join(docs),100)
                if "memory" in pgt or "cores" in pgt:
                   break

            elif target in ["Amazon EC2", "AWS Lambda","Amazon DynamoDB"]:
              print(docs)
              pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself, along with its configuration. Otherwise just state the name of the alternate service along with its configuration. Do not unnecessarily predict other services if the current is sufficient. So if the latency of the service is less than the workload latency, predict "+serv+" with its configuration. This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But definately provide the configuration of the service. Do not miss out on the configuration. \nDocuments:\n"+"\n".join(docs),100)
            elif "S3" in target:
              print(docs)
              pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself. Otherwise just state the name of the alternate service along with its configuration. If both "+serv+" and the new service has latency less than the workload latency, always predict the service with lesser cost,never predict "+serv+". This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration.In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But if you can from the documents, definately provide the configuration if the new service. \nDocuments:\n"+"\n".join(docs),100)
            else:
               pgt=self.getresponse("I have a query. Please answer the query. I have provided some documents. Please answer the query from the documents provided only. Do Not use your own knowledge to answer the query. Only answer the query by using the information provided in the documents I have provided below.\nQuery:I have workload requirement, that I need latency of "+latency+" seconds and throughput of "+throughput+" requests per second and memory of "+memory+" MB. Is "+serv+" sufficient for that workload? For this you need to check whether the workload parameters are within the service parameters. If no what other configuration of "+serv+" is needed or state which other alternative service can be used instead of "+serv+" for this? If the answer is that "+serv+" is suuficient, output "+serv+" itself, along with its configuration. Otherwise just state the name of the alternate service along with its configuration. Do not unnecessarily predict other services if the current is sufficient. So if the latency of the service is less than the workload latency, predict "+serv+" with its configuration. This is first priority. Second priority is to check if a lower configuration of "+serv+" is sufficient. If yes then mention the new configuration. If lower configuration is sufficient, then never predict new service. In this case just output the new configuration. If none of the services in the documents meet the latency criteria, first try to check which service or configuration has lowest latency. If latency is also same, mention the service or the configuration that has lowest cost per hour. So in this case, do not mention the configuration that has the higher cost per hour. Do not write any explanation. Do not write any excepts from the documents. And only reply with services or configuration from the documents. Do not write any service that is not in the documents. But definately provide the configuration of the service. Do not miss out on the configuration. \nDocuments:\n"+"\n".join(docs),100)
            self.progress_bar.setValue(l+5)
              #QApplication.processEvents()
            l+=10
            mappy[serv]=pgt
            #if "S3" in serv:
            #  print(newservs)
            
            print("-------------------------")
        print(mappy)
        
        
        #print(f"Maximum Latency (Makespan): {max_latency:.2f} seconds")
        #print(f"Total Cost for {workload_invocations} invocations: ${total_cost:.8f}")
        #ischange=False
         
        self.progress_bar.setValue(l+5)
              #QApplication.processEvents()
        l+=10
        
        for k,v in mappy.items():
          extra=""
          if "ElastiCache" in v:
            newmem=0
            if "1024" in v and " 1 " in v: 
              newmem=1024
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.micro"})
            elif "2048" in v and " 1 " in v:
              newmem=2048
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.small"})
            elif "1024" in v and " 2 " in v:
              newmem=1024
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.micro"})
            elif "2048" in v and " 2 " in v:
              newmem=2048
              dictelasticachenew=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.small"})
            print(v)
            if currentmemory=="1024" and cores=="1": 
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.micro"})
            elif currentmemory=="2048" and cores=="1":
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t2.small"})
            elif currentmemory=="1024" and cores=="2":
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.micro"})
            elif currentmemory=="2048" and cores=="2":
              dictelasticacheold=compute_elasticache_latency_cost({"concurrency":throughput,"elasticache_instance_type":"t3.small"})
            
            if float(dictelasticacheold["latency"])<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(dictelasticachenew["latency"])<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(dictelasticacheold["cost"],dictelasticacheold["latency"],str(currentmemory),dictelasticachenew["cost"],dictelasticachenew["latency"],str(newmem),"Amazon ElastiCache","Amazon ElastiCache",throughput,waswithin,iswithin,"replace",False) 
          if "DynamoDB" in k and "DAX" in v:
            dictdynamo=compute_dynamodb_latency_and_cost({"workload":throughput,"data_size":100000,"chunk_size":10000})
            cost=dictdynamo["cost"]
            max_latency=dictdynamo["latency"]
            print(cost)
            print(max_latency)
            costdax=cost*2
            max_latency_dax=0.01
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            if float(max_latency)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(max_latency_dax)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(cost,max_latency,currentmemory,costdax,max_latency_dax,lambdamemreq,"Amazon DynamoDB","Amazon DynamoDB Accelerator (DAX)",throughput,waswithin,iswithin,"insert",False) 
          elif "EC2" in k and "EC2" not in v:
            z=(0.05625*int(throughput))-(0.05625*int(ec2cores))+0.45
            zc=1.76
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            if float(z)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(max_latency)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(zc,z,currentmemory,total_cost,max_latency,lambdamemreq,"Amazon EC2","AWS Lambda",throughput,waswithin,iswithin,"replace",False)  
          elif "Lambda" in k and "Lambda" in v:
            dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(currentmemory), "function_defn":"DTS_deepreader","memory_required":int(memory)})
            max_latency=dictlambda["latency"]
            total_cost=dictlambda["cost"]
            print(currentmemory)
            print(memory)
            print(dictlambda)
            self.progress_bar.setValue(l+5)
            dictlambda=compute_lambda_latency_and_cost({"workload":int(throughput), "memory_mb":int(lambdamemreq), "function_defn":"DTS_deepreader","memory_required":int(memory)})
            z=dictlambda["latency"]
            zc=dictlambda["cost"]
            print(lambdamemreq)
            print(memory)
            print(dictlambda)
            self.progress_bar.close()
            if float(z)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(max_latency)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(total_cost,max_latency,currentmemory,z,zc,lambdamemreq,"AWS Lambda","AWS Lambda",throughput,waswithin,iswithin,"replace",False)
          elif "S3" in k and "Glacier" in v:
            dicts3 = compute_s3_latency_and_cost({"workload" : int(throughput), "memoryConfig" : int(currentmemory) , "fileSize" : 20480, "operation": "read"})
            costval=dicts3["cost"]
            max_latency_sec=dicts3["latency"]
            costval=0.49
            z=60
            zc=0.35*costval
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            if float(max_latency_sec)<=float(latency):
               waswithin=False
            else:
               waswithin=True
            if float(z)<=float(latency):
               iswithin=False
            else:
               iswithin=True
            self.msgbox(costval,max_latency_sec,currentmemory,zc,z,lambdamemreq,"Amazon Simple Storage System (S3)","Amazon S3 Glacier",throughput,waswithin,iswithin,"replace",False)
          elif "EC2" in v:
            self.progress_bar.setValue(l+5)
            self.progress_bar.close()
            self.msgbox(None,None,None,None,None,None,"Amazon EC2","Amazon EC2",None,None,None,None,False)
          else:
            print("000000000000000000000000000000000000000000000")
            self.progress_bar.close()
            self.msgbox(None,None,currentmemory,None,None,None,k,v,None,None,None,None,True)
          
            
              
        
        #lambdashape=Shape("AWS Lambda",0,0,total_cost,max_latency,251)
        #if ischange == True:
          
          
            
        
        
        
