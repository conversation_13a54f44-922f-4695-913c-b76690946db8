"""
ML prediction endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from typing import Dict, Any

from app.models.requests import (
    LambdaCostRequest, S3CostRequest, DynamoDBCostRequest, APIGatewayCostRequest, EC2CostRequest, EKSCostRequest,
    EC2RecommendationRequest, ArchitectureCostRequest
)
from app.models.responses import (
    LambdaCostResponse, S3CostResponse, DynamoDBCostResponse, APIGatewayCostResponse, EC2CostResponse, EKSCostResponse,
    EC2RecommendationResponse, ErrorResponse, SuccessResponse,
    ArchitectureCostResponse
)
from app.services.ml_service import MLService
from app.services.aws_worksheet_service import aws_worksheet_service


router = APIRouter()


def get_ml_service(request: Request) -> MLService:
    """Dependency to get ML service from app state"""
    return request.app.state.ml_service


@router.post("/predict-lambda-cost", response_model=LambdaCostResponse)
async def predict_lambda_cost(
    request: LambdaCostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict Lambda function cost and latency

    This endpoint uses machine learning models to predict the cost and latency
    of AWS Lambda functions based on configuration parameters.
    """
    try:
        result = ml_service.predict_lambda_cost(
            workload=request.workload_invocations,  # Map to PyQt5 parameter name
            memory_mb=request.memory_mb,
            function_defn=request.function_purpose,  # Map to PyQt5 parameter name
            memory_required=request.memory_required
        )

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return LambdaCostResponse(
            latency=result['latency'],
            cost=result['cost'],
            max_latency=result['max_latency'],
            total_execution_cost=result['total_execution_cost'],
            total_invocation_cost=result['total_invocation_cost'],
            memory_gb=result['memory_gb'],
            details=result.get('details')
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/predict-s3-cost", response_model=S3CostResponse)
async def predict_s3_cost(
    request: S3CostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict S3 storage cost and latency

    This endpoint uses machine learning models to predict the cost and latency
    of AWS S3 operations based on file size, memory, and operation type.
    """
    try:
        result = ml_service.predict_s3_cost(
            workload=request.workload,
            fileSize=request.file_size,  # Map to PyQt5 parameter name
            memoryConfig=request.memory,  # Map to PyQt5 parameter name
            operation=request.operation
        )

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return S3CostResponse(
            latency=result['latency'],
            cost=result['cost'],
            predicted_latencies=result['predicted_latencies'],
            operation_type=result['operation_type'],
            details=result.get('details')
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/predict-dynamodb-cost", response_model=DynamoDBCostResponse)
async def predict_dynamodb_cost(
    request: DynamoDBCostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict DynamoDB cost and latency

    This endpoint uses machine learning models to predict the cost and latency
    of AWS DynamoDB operations based on workload and configuration parameters.
    """
    try:
        # Use PyQt5 parameter names directly
        result = ml_service.predict_dynamodb_cost(
            workload=request.workload,
            data_size=request.data_size,
            mem_config=request.mem_config,
            chunk_size=request.chunk_size,
            num_tables=request.num_tables,
            num_threads=request.num_threads
        )

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return DynamoDBCostResponse(
            latency=result['latency'],
            cost=result['cost'],
            write_cost=result['write_cost'],
            storage_cost=result['storage_cost'],
            total_item_size_mb=result['total_item_size_mb'],
            details=result.get('details')
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/predict-apigateway-cost", response_model=APIGatewayCostResponse)
async def predict_apigateway_cost(
    request: APIGatewayCostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict API Gateway cost and latency

    This endpoint calculates API Gateway costs based on request volume and payload sizes.
    Uses exact PyQt5 parameter names and default values for compatibility.
    """
    try:
        result = ml_service.predict_apigateway_cost(
            requests_per_hour=request.requests_per_hour,
            input_payload_size_kb=request.input_payload_size_kb,
            output_payload_size_kb=request.output_payload_size_kb
        )

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return APIGatewayCostResponse(
            latency=result['latency'],
            cost=result['cost'],
            requests_per_hour=result['requests_per_hour'],
            input_payload_size_kb=result['input_payload_size_kb'],
            output_payload_size_kb=result['output_payload_size_kb'],
            details=result.get('details')
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/predict-ec2-cost", response_model=EC2CostResponse)
async def predict_ec2_cost_pyqt5(
    request: EC2CostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict EC2 cost and latency using PyQt5-exact accelerator-based calculation

    This endpoint calculates EC2 costs using the exact same accelerator-based method
    as PyQt5 MapleGUI with instanceType, LLMModel, batchSize, inputTokens, outputTokens.
    """
    try:
        # Use PyQt5-exact calculation method
        cost, latency = ml_service._calculate_ec2_cost_pyqt5_exact({
            'instanceType': request.instanceType,
            'LLMModel': request.LLMModel,
            'batchSize': request.batchSize,
            'inputTokens': request.inputTokens,
            'outputTokens': request.outputTokens
        })

        return EC2CostResponse(
            latency=latency,
            cost=cost,
            instanceType=request.instanceType,
            LLMModel=request.LLMModel,
            batchSize=request.batchSize,
            inputTokens=request.inputTokens,
            outputTokens=request.outputTokens,
            details={"method": "pyqt5_accelerator_based"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/predict-ec2-cost-legacy", response_model=EC2CostResponse)
async def predict_ec2_cost(
    request: EC2CostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict EC2 cost and latency using real AWS pricing data

    This endpoint uses real AWS pricing data from Mumbai region to calculate
    EC2 costs including compute and EBS storage costs.
    """
    try:
        # Convert request to input format
        input_from_ui = {}

        if request.instances:
            # Multiple instances mode
            instances_list = []
            for instance in request.instances:
                instances_list.append({
                    'instance_type': instance.instance_type,
                    'number': instance.number,
                    'duration': instance.duration,
                    'ebs_size': instance.ebs_size,
                    'ebs_type': instance.ebs_type
                })
            input_from_ui['instances'] = instances_list
        else:
            # Single instance mode
            input_from_ui = {
                'instance_type': request.instance_type,
                'number': request.number,
                'duration': request.duration,
                'ebs_size': request.ebs_size,
                'ebs_type': request.ebs_type
            }

        result = ml_service.predict_ec2_cost(input_from_ui)

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return EC2CostResponse(
            latency=result['latency'],
            cost=result['cost'],
            instance_details=result['instance_details'],
            total_instances=result['total_instances'],
            pricing_source=result['pricing_source'],
            details=result.get('details')
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/ec2-recommendations", response_model=EC2RecommendationResponse)
async def get_ec2_recommendations(
    request: EC2RecommendationRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Get EC2 instance recommendations based on requirements

    This endpoint provides EC2 instance recommendations based on vCPU and memory
    requirements, sorted by cost-effectiveness.
    """
    try:
        result = ml_service.get_ec2_recommendations(
            vcpu_requirement=request.vcpu_requirement,
            memory_requirement_gb=request.memory_requirement_gb
        )

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return EC2RecommendationResponse(
            recommendations=result['recommendations'],
            total_recommendations=result['total_recommendations'],
            vcpu_requirement=request.vcpu_requirement,
            memory_requirement_gb=request.memory_requirement_gb
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/ec2-instance-pricing/{instance_type}")
async def get_ec2_instance_pricing(
    instance_type: str,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Get pricing information for a specific EC2 instance type

    Returns detailed pricing and specification information for the requested instance type.
    """
    try:
        result = ml_service.get_ec2_instance_pricing(instance_type)

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return SuccessResponse(
            message=f"EC2 instance pricing retrieved for {instance_type}",
            data=result
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/predict-s3-transfer-cost")
async def predict_s3_transfer_cost(
    data_size_gb: float,
    transfer_type: str = "out",
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Predict S3 data transfer costs

    Calculate the cost of transferring data to/from S3 based on data size and transfer type.
    """
    try:
        result = ml_service.predict_s3_transfer_cost(data_size_gb, transfer_type)

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return SuccessResponse(
            message="S3 transfer cost predicted successfully",
            data=result
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/estimate-dynamodb-capacity")
async def estimate_dynamodb_capacity(
    items_per_second: int,
    item_size_kb: float,
    operation_type: str = "write",
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Estimate DynamoDB capacity requirements

    Calculate the required read/write capacity units for DynamoDB based on workload characteristics.
    """
    try:
        result = ml_service.estimate_dynamodb_capacity(items_per_second, item_size_kb, operation_type)

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return SuccessResponse(
            message="DynamoDB capacity estimated successfully",
            data=result
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/calculate-cost", response_model=ArchitectureCostResponse)
async def calculate_architecture_cost(
    request: ArchitectureCostRequest,
    ml_service: MLService = Depends(get_ml_service)
):
    """
    Calculate cost and latency for entire architecture - mimics PyQt5 flow

    This endpoint replicates the PyQt5 MapleGUI cost calculation flow:
    1. Calculates individual service costs using ML models
    2. Aggregates costs based on architecture type (Monolith/Microservices)
    3. Includes network and VPC costs
    4. Provides detailed breakdown and recommendations
    """
    try:
        result = ml_service.calculate_architecture_cost(
            architecture_type=request.architecture_type,
            nodes=request.nodes,
            edges=request.edges,
            architecture_data=request.architecture
        )

        if result['status'] == 'error':
            raise HTTPException(status_code=400, detail=result['error'])

        return ArchitectureCostResponse(
            total_cost=result['total_cost'],
            total_latency=result['total_latency'],
            cost_per_request=result['cost_per_request'],
            cost_per_1000_requests=result['cost_per_1000_requests'],
            architecture_type=result['architecture_type'],
            service_breakdown=result['service_breakdown'],
            network_costs=result['network_costs'],
            vpc_costs=result['vpc_costs'],
            recommendations=result.get('recommendations'),
            details=result.get('details')
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/ml-service-status")
async def get_ml_service_status(ml_service: MLService = Depends(get_ml_service)):
    """
    Get the status of all ML services

    Returns the health status of all machine learning prediction services.
    """
    try:
        status = ml_service.get_service_status()
        return SuccessResponse(
            message=status['message'],
            data=status
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/worksheet-values")
async def get_worksheet_values():
    """
    Get initial cost and latency values from AWS worksheet

    This endpoint provides the same initial values that PyQt5 MapleGUI services
    get from the Excel worksheet (CS-arch.xlsx) before any ML calculations are performed.
    The values are read using the exact same approach as PyQt5 MapleGUI.
    """
    try:
        worksheet_values = aws_worksheet_service.get_all_services()

        # Add debug information
        total_services = len(worksheet_values)
        ec2_cost = aws_worksheet_service.get_initial_cost('aws-ec2')

        return SuccessResponse(
            message=f"AWS worksheet values retrieved successfully ({total_services} services loaded, EC2 cost: {ec2_cost})",
            data=worksheet_values
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/worksheet-values/{service_id}")
async def get_service_worksheet_values(service_id: str):
    """
    Get initial cost and latency values for a specific service

    Args:
        service_id: The service identifier (e.g., 'aws-ec2')
    """
    try:
        service_values = aws_worksheet_service.get_initial_values(service_id)

        if not service_values:
            raise HTTPException(
                status_code=404,
                detail=f"No worksheet values found for service: {service_id}"
            )

        return SuccessResponse(
            message=f"Worksheet values for {service_id} retrieved successfully",
            data=service_values
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/calculate-worksheet-cost")
async def calculate_worksheet_cost(request: dict):
    """
    Calculate cost using Excel worksheet method for AWS services without ML functions
    This replicates the PyQt5 MapleGUI Excel-based cost calculation approach
    """
    try:
        service_id = request.get('serviceId', '')
        config = request.get('config', {})

        # Service-specific cost calculations based on PyQt5 MapleGUI approach
        cost = 0.0
        latency = 100.0  # Default latency in ms

        # Map service IDs to cost calculations
        if service_id in ['amazon-athena']:
            # Athena: $5 per TB of data scanned
            data_scanned = config.get('dataScanned', 100)  # GB
            workload = config.get('workload', 1000)  # queries
            cost = (data_scanned / 1000) * 5 * (workload / 1000)  # Convert GB to TB
            latency = 2000 + (data_scanned * 10)  # Query latency increases with data size

        elif service_id in ['amazon-cloudsearch']:
            # CloudSearch: Based on instance hours and search requests
            cost = 0.12 * 24 * 30  # Small instance for a month
            latency = 50

        elif service_id in ['amazon-elasticsearch-service']:
            # Elasticsearch: Based on instance type and storage
            cost = 0.18 * 24 * 30  # t3.small.elasticsearch for a month
            latency = 25

        elif service_id in ['amazon-emr']:
            # EMR: Based on instance hours
            instance_count = config.get('instanceCount', 3)
            cost = 0.27 * 24 * 30 * instance_count  # m5.xlarge instances
            latency = 5000  # Job processing latency

        elif service_id in ['amazon-kinesis']:
            # Kinesis: Based on shard hours and PUT payload units
            shard_count = config.get('shardCount', 1)
            workload = config.get('workload', 1000)
            cost = (shard_count * 0.015 * 24 * 30) + (workload * 0.000014)
            latency = 200

        elif service_id in ['amazon-redshift']:
            # Redshift: Based on node hours
            node_count = config.get('nodeCount', 2)
            cost = 0.25 * 24 * 30 * node_count  # dc2.large nodes
            latency = 1000

        elif service_id in ['amazon-quicksight']:
            # QuickSight: Per user pricing
            cost = 9.0  # Standard user per month
            latency = 500

        elif service_id in ['aws-glue']:
            # Glue: Based on DPU hours
            cost = 0.44 * 10  # 10 DPU hours
            latency = 30000  # ETL job latency

        elif service_id in ['amazon-eventbridge']:
            # EventBridge: Based on custom events
            custom_events = config.get('customEvents', 1000000)
            cost = max(0, (custom_events - 100000000) * 0.000001)  # First 100M free
            latency = 50

        elif service_id in ['amazon-mq']:
            # MQ: Based on broker instance hours
            cost = 0.30 * 24 * 30  # mq.t3.micro
            latency = 10

        elif service_id in ['amazon-simple-notification-service']:
            # SNS: Based on requests and notifications
            requests = config.get('publishRequests', 1000000)
            cost = requests * 0.0000005  # $0.50 per million requests
            latency = 100

        elif service_id in ['amazon-simple-queue-service']:
            # SQS: Based on requests
            requests = config.get('requests', 1000000)
            cost = max(0, (requests - 1000000) * 0.0000004)  # First 1M free
            latency = 50

        elif service_id in ['aws-iot-core']:
            # IoT Core: Based on messages
            messages = config.get('workload', 1000000)
            cost = messages * 0.000001  # $1 per million messages
            latency = 200

        elif service_id in ['aws-greengrass']:
            # Greengrass: Based on core devices
            core_devices = config.get('coreDevices', 10)
            cost = core_devices * 0.16 * 24 * 30  # Per core device per month
            latency = 100

        elif service_id in ['amazon-comprehend']:
            # Comprehend: Based on characters processed
            characters = config.get('charactersProcessed', 100000)
            cost = (characters / 10000) * 0.0001  # Per 100 characters
            latency = 1000

        elif service_id in ['amazon-rekognition']:
            # Rekognition: Based on images processed
            images = config.get('workload', 1000)
            cost = images * 0.001  # $1 per 1000 images
            latency = 500

        elif service_id in ['amazon-textract']:
            # Textract: Based on pages processed
            pages = config.get('workload', 1000)
            cost = pages * 0.0015  # $1.50 per 1000 pages
            latency = 2000

        elif service_id in ['amazon-cloudwatch']:
            # CloudWatch: Based on metrics and logs
            metrics = config.get('workload', 10000)
            log_data = config.get('logData', 10)
            cost = (metrics * 0.0003) + (log_data * 0.50)  # Metrics + log ingestion
            latency = 100

        elif service_id in ['aws-cloudformation']:
            # CloudFormation: Based on stack operations
            operations = config.get('stackOperations', 100)
            cost = operations * 0.0009  # Per handler operation
            latency = 5000

        elif service_id in ['aws-cloudtrail']:
            # CloudTrail: Based on events
            events = config.get('workload', 100000)
            cost = max(0, (events - 100000) * 0.000001)  # First 100K free
            latency = 200

        else:
            # Default calculation for unknown services
            workload = config.get('workload', 1000)
            cost = workload * 0.0001  # Generic cost calculation
            latency = 100

        # Ensure minimum cost
        cost = max(cost, 0.001)

        result = {
            "cost": round(cost, 6),
            "latency": round(latency, 2),
            "message": f"Calculated using Excel worksheet method (PyQt5 compatible)",
            "service": service_id,
            "method": "worksheet_calculation"
        }

        return result

    except Exception as e:
        # Return fallback values instead of raising exception
        return {
            "cost": 0.1,
            "latency": 100.0,
            "message": f"Fallback cost estimate for {request.get('serviceId', 'unknown service')}",
            "error": str(e),
            "method": "fallback_calculation"
        }