#!/usr/bin/env python3
"""
Generate complete AWS services catalog from PyQt5 MapleGUI awsservices.txt
This script creates the complete service definitions for the web application
"""

import re
import json

# Read the PyQt5 awsservices.txt file
def parse_aws_services():
    """Parse awsservices.txt and extract all service information"""
    services = []
    
    # Service data from PyQt5 MapleGUI awsservices.txt
    aws_services_data = """Amazon Athena^8^8
Amazon Cloudsearch^5^1
Amazon Elasticsearch Service^7^10
Amazon EMR^2^3
Amazon FinSpace^4^3
Amazon Kinesis^8^9^Latency:W-78ms, R-690ms; Throughput: 2MB/s^Latency:W-78ms, R-690ms; Throughput: 2MB/s^https://calculator.aws/#/estimate?id=f974f58c494dcbc2181a3c3bd75fbae945c05c13^25USD/month for 10 records/s^Amazon EC2
Amazon Managed Streaming for Apache Kafka^6^7
Amazon Redshift^8^8
Amazon QuickSight^2^9
AWS Data Exchange^2^4
AWS Data Pipeline^8^3
AWS Glue^5^7
AWS Lake Formation^8^1
AWS Step Functions^8^10
Amazon AppFlow^7^1
Amazon EventBridge^10^5
Amazon Managed Workflows for Apache Airflow^3^6
Amazon MQ^7^2
Amazon Simple Notification Service^4^2
Amazon Simple Queue Service^5^5
Amazon AppSync^2^9
Amazon Managed Blockchain^5^6
Amazon Quantum Ledger Database (QLDB)^2^6
Alexa for Business^7^5
Amazon Chime^9^1
Amazon Honeycode^4^6
Amazon WorkDocs^6^6
Amazon WorkMail^8^8
AWS Cost Explorer^5^1
AWS Budgets^4^6
AWS Cost and Usage Report^1^9
Reserved Instance Reporting^4^10
Savings Plans^8^4
Amazon EC2^7^6
Amazon EC2 Auto Scaling^4^5
AWS App Runner^5^7
AWS Batch^9^3
AWS Elastic Beanstalk^3^9
AWS Outposts^9^5
AWS Serverless Application Repository^8^6
AWS Snow Family^6^9
AWS Wavelength^10^7
VMWare Cloud on AWS^7^10
Amazon Elastic Container Registry^9^6
Amazon Elastic Container Service (ECS)^2^8
Amazon ECS Anywhere^9^9
Amazon Elastic Kubernetes Service^2^1
Amazon EKS Distro^8^3
AWS App2Container^5^2
AWS Copilot^1^8
AWS Fargate^5^3
Red Hat OpenShift Service on AWS^1^8
Amazon Connect^2^3
Amazon Pinpoint^1^4
Amazon Simple Email Service^1^3
Amazon Aurora^1^8
Amazon DynamoDB^7^6
Amazon DocumentDB^1^10
Amazon ElastiCache^6^2
Amazon Keyspaces^10^9
Amazon Neptune^4^5
Amazon Quantum Ledger Database^8^6
Amazon RDS^3^9
Amazon RDS on VMware^2^6
Amazon Timestream^5^9
Amazon Database Migration Service^6^1
AWS Glue^2^10
AWS Developer Tools^10^2
Amazon CodeGuru^10^2
Amazon Correto^3^8
AWS Cloud Development Kit^4^6
AWS Cloud9^4^1
AWS CloudShell^4^9
AWS CodeArtifact^8^8
AWS CodeBuild^7^4
AWS CodeCommit^3^7
AWS CodeDeploy^10^1
AWS CodePipeline^5^7
AWS CodeStar^5^10
AWS Command Line Interface^5^3
AWS Device Farm^10^9
AWS Fault Injection Simulator^10^1
AWS Tools and SDKs^6^4
AWS X-Ray^10^3
Amazon AppStream 2.0^9^10
Amazon WorkDocs^7^5
Amazon WorkLink^9^6
Amazon WorkSpaces^9^2
AWS Amplify^8^6
Amazon API Gateway^5^6^Appx. Additional 100ms/request^Appx. Additional 100ms/request^https://calculator.aws/#/estimate?id=b54521fb63d070d2975080cd4b67328123e12f7a^1.05 USD/month/million requests
Amazon Location Service^7^6
Amazon Pinpoint^1^4
AWS Device Farm^7^2
Amazon GameLift^6^7
Amazon Lumberyard^9^3
AWS IoT Core^1^6^Its is just  a gateway . Devices can talk to Kinesis directly as well^Its is just  a gateway . Devices can talk to Kinesis directly as well^https://calculator.aws/#/estimate?id=74ead12447a1c420fabd8b56b95fe1288f905a95^1 USD for 1  millions in a month
AWS Greengrass^9^4
AWS IoT 1-Click^2^8
AWS IoT Analytics^2^3
AWS IoT Button^5^2
AWS IoT Device Defender^10^1
AWS IoT Management^5^9
AWS IoT Events^7^4
AWS IoT SiteWise^9^7
AWS IoT Things Graph^5^8
AWS Partner Device Catalog^10^8
FreeRTOS^3^3
Amazon SageMaker^1^5
Amazon Augmented AI^3^6
Amazon CodeGuru^1^10
Amazon Comprehend^3^7
Amazon DevOps Guru^10^7
Amazon Elastic Inference^1^1
Amazon Forecast^6^4
Amazon Fraud Detector^10^4
Amazon Healthlake^2^9
Amazon Kendra^9^1
Amazon Lex^4^4
Amazon Lookout for Equipment^3^1
Amazon Lookout for Metrics^7^6
Amazon Lookout for Vision^2^9
Amazon Minitron^7^7
Amazon Personalize^3^5
Amazon Polly^7^6
Amazon Rekognition^4^4
Amazon SageMaker Data Wrangler^8^8
Amazon SageMaker Ground Truth^6^2
Amazon Textract^8^4
Amazon Translate^6^1
Amazon Transcribe^6^8
Amazon Deep Learning AMIs^8^9
AWS Deep Learning Containers^7^4
AWS DeepComposer^7^8
AWS DeepLens^8^8
AWS DeepRacer^2^10
AWS Inferentia^10^4
AWS Panorama^8^6
PyTorch on AWS^6^3
Apache MXNet on AWS^1^10
TensorFlow on AWS^5^1
Amazon CloudWatch^3^4
AWS Auto Scaling^10^10
AWS Chatbot^7^4
AWS CloudFormation^7^5
AWS CloudTrail^7^6
AWS Compute Optimizer^3^3
AWS Config^6^6
AWS Control Tower^7^2
AWS Console Mobile Application^4^1
AWS Distro for OpenTelemetry^2^4
AWS Launch Wizard^2^8
AWS License Manager^1^10
AWS Management Console^10^5
Amazon Managed Service for Grafana^8^9
AWS Management Service for Prometheus^10^7
AWS OpsWorks^2^3
AWS Organizations^9^8
AWS Personal Health Dashboard^9^4
AWS Proton^4^4
AWS Service Catalog^5^8
AWS Systems Manager^8^4
AWS Trusted Advisor^9^1
AWS Well-Architected Tool^9^6
Amazon Elastic Transcoder^10^6
Amazon Interactive Video Service^3^1
Amazon Kinesis Video Streams^4^3
AWS Elemental MediaConnect^4^8
AWS Elemental MediaConvert^4^4
AWS Elemental MediaLive^9^10
AWS Elemental MediaPackage^2^7
AWS Elemental MediaStore^9^7
AWS Elemental MediaTailor^3^7
AWS Elemental Appliances and Software^7^6
AWS Nimble Studio^4^10
AWS Migration Hub^8^8
AWS Application Discovery Service^4^4
AWS Application Migration Service (CloudEndure Migration)^5^9
AWS Database Migration Service^4^3
AWS Transfer Family^10^9
Migration Evaluator (formerly TSO Logic)^1^7
Amazon VPC^6^6
Amazon CloudFront^3^1
Amazon Route 53^4^1
Amazon PrivateLink^4^9
AWS App Mesh^9^1
AWS Cloud Map^9^6
AWS Direct Connect^1^2
AWS Global Accelerator^8^8
AWS Transit Gateway^2^4
Elastic Load Balancing^8^3
Amazon VPC^5^7
Amazon CloudFront^2^10
Amazon Route 53^9^8
Amazon PrivateLink^7^1
AWS App Mesh^2^6
AWS Cloud Map^9^9
AWS Direct Connect^6^7
AWS Global Accelerator^7^3
AWS Storage Gateway^4^1
Elastic Load Balancing^1^5
Amazon Braket^6^5
AWS RoboMaker^7^8
AWS Ground Station^6^4
AWS Identity & Access Management^6^8
Amazon Cognito^7^5
Amazon Detective^5^3
Amazon GuardDuty^7^1
Amazon Inspector^5^4
Amazon Macie^4^9
AWS Artifact^2^2
AWS Audit Manager^7^5
AWS Certificate Manager^7^8
AWS CloudHSM^2^3
AWS Directory Service^2^8
AWS Firewall Manager^2^1
AWS Resource Access Manager^8^6
AWS Secrets Manager^9^6
AWS Security Hub^10^9
AWS Shield^1^7
AWS Single Sign-On^2^7
AWS WAF^2^1
AWS Lambda^1^5
Amazon DynamoDB^8^5
Amazon EventBridge^8^9
Amazon Simple Notification System (SNS)^10^8
Amazon Simple Queue Service (SQS)^8^1
AWS Fargate^7^4
AWS Step Functions^9^7
Amazon Simple Storage System (S3)^5^2^Latency:100-200ms small object latency: Throughput:3500 PUT/POST, 5500 GET requests/s^Latency:100-200ms small object latency: Throughput:3500 PUT/POST, 5500 GET requests/s^https://calculator.aws/#/estimate?id=bf51a21deb5b14f8a5b0d2ac8dda65e725a0ddc0^25 USD for 1 GB/month and 1000 GET, PUT etc requests
Amazon Elastic Book Store (EBS)^6^9
Amazon Elastic File System (EFS)^10^5
Amazon FSx for Lustre^7^4
Amazon FSx for Windows File Server^4^7
Amazon S3 Glacier^5^5
AWS Backup^3^3
AWS Snow Family^8^5
AWS Storage Facility^3^1
CloudEnsure Disaster Recovery^6^3
Amazon Sumerian^7^6
Amazon Kinesis Data Firehose^4^7^10MB/s read throughput^10MB/s read throughput^https://calculator.aws/#/estimate?id=2bb8151c075c5adfde53768cda344c86099b0d43^425 USD/month for 1000 record/s ingestion
User^0^0
Amazon DynamoDB Accelerator (DAX)^0^0"""

    # Parse each line
    for line in aws_services_data.strip().split('\n'):
        if line.strip():
            parts = line.split('^')
            service_name = parts[0].strip()
            
            # Generate service ID from name
            service_id = service_name.lower().replace(' ', '-').replace('(', '').replace(')', '').replace('&', 'and')
            
            # Determine category based on service name
            category = determine_category(service_name)
            
            # Generate icon
            icon = determine_icon(service_name, category)
            
            # Generate default config
            default_config = generate_default_config(service_name, category)
            
            service = {
                'id': service_id,
                'name': service_name,
                'provider': 'AWS',
                'category': category,
                'icon': icon,
                'description': f'{service_name} service',
                'defaultConfig': default_config,
                'costModel': f'{service_id.replace("-", "_")}_cost_model',
                'latencyModel': f'{service_id.replace("-", "_")}_latency_model',
                'color': '#FF9900'
            }
            
            services.append(service)
    
    return services

def determine_category(service_name):
    """Determine service category based on name"""
    name_lower = service_name.lower()
    
    if any(word in name_lower for word in ['athena', 'emr', 'kinesis', 'redshift', 'quicksight', 'glue', 'data']):
        return 'Analytics'
    elif any(word in name_lower for word in ['ec2', 'lambda', 'fargate', 'batch', 'ecs', 'eks']):
        return 'Compute'
    elif any(word in name_lower for word in ['s3', 'ebs', 'efs', 'glacier', 'storage']):
        return 'Storage'
    elif any(word in name_lower for word in ['rds', 'dynamodb', 'aurora', 'database']):
        return 'Database'
    elif any(word in name_lower for word in ['vpc', 'cloudfront', 'route', 'load balancing']):
        return 'Networking'
    elif any(word in name_lower for word in ['iam', 'cognito', 'security', 'guard', 'shield']):
        return 'Security'
    elif any(word in name_lower for word in ['sagemaker', 'comprehend', 'rekognition', 'ml', 'ai']):
        return 'AI/ML'
    elif any(word in name_lower for word in ['iot', 'greengrass']):
        return 'IoT'
    elif any(word in name_lower for word in ['cloudwatch', 'cloudformation', 'config']):
        return 'Management'
    elif any(word in name_lower for word in ['code', 'developer']):
        return 'Developer Tools'
    elif any(word in name_lower for word in ['sns', 'sqs', 'eventbridge', 'mq']):
        return 'Application Integration'
    elif any(word in name_lower for word in ['cost', 'budget']):
        return 'Cost Management'
    else:
        return 'Other'

def determine_icon(service_name, category):
    """Determine appropriate icon for service"""
    name_lower = service_name.lower()
    
    # Try to use official AWS icons first
    if 'lambda' in name_lower:
        return '/icons/aws/AWS Lambda.png'
    elif 'ec2' in name_lower:
        return '/icons/aws/Amazon EC2.png'
    elif 's3' in name_lower:
        return '/icons/aws/Amazon Simple Storage System (S3).png'
    elif 'dynamodb' in name_lower:
        return '/icons/aws/Amazon DynamoDB.png'
    elif 'api gateway' in name_lower:
        return '/icons/aws/Amazon API Gateway.png'
    
    # Fallback to category-based icons
    category_icons = {
        'Analytics': '📊',
        'Compute': '🖥️',
        'Storage': '💾',
        'Database': '🗄️',
        'Networking': '🌐',
        'Security': '🔒',
        'AI/ML': '🤖',
        'IoT': '📡',
        'Management': '⚙️',
        'Developer Tools': '🛠️',
        'Application Integration': '🔗',
        'Cost Management': '💰',
        'Other': '☁️'
    }
    
    return category_icons.get(category, '☁️')

def generate_default_config(service_name, category):
    """Generate default configuration for service"""
    base_config = {'workload': 1000}
    
    name_lower = service_name.lower()
    
    if 'lambda' in name_lower:
        base_config.update({
            'memory_mb': 1024,
            'timeout': 30,
            'runtime': 'nodejs18.x'
        })
    elif 'ec2' in name_lower:
        base_config.update({
            'instanceType': 't3.micro',
            'instanceCount': 1
        })
    elif 's3' in name_lower:
        base_config.update({
            'storageClass': 'Standard',
            'storageGB': 100
        })
    elif 'dynamodb' in name_lower:
        base_config.update({
            'readCapacity': 5,
            'writeCapacity': 5
        })
    
    return base_config

if __name__ == '__main__':
    services = parse_aws_services()
    print(f"Generated {len(services)} AWS services")
    
    # Output TypeScript service definitions
    with open('generated_services.ts', 'w') as f:
        f.write("// Auto-generated AWS services from PyQt5 MapleGUI\n")
        f.write("export const GENERATED_AWS_SERVICES = [\n")
        for service in services:
            f.write(f"  {json.dumps(service, indent=2)},\n")
        f.write("]\n")
