#!/usr/bin/env python3
"""
Test script to verify the export/import fix for connections.
This script simulates the export/import process to ensure connections are preserved.
"""

import json
import tempfile
import os
from typing import Dict, Any

# Sample architecture data that would come from the frontend
sample_architecture_data = {
    "nodes": [
        {
            "id": "node-1",
            "type": "userNode",
            "position": {"x": 100, "y": 100},
            "data": {
                "service": {"id": "user", "name": "User"},
                "config": {},
                "label": "User",
                "cost": 0,
                "latency": 0
            }
        },
        {
            "id": "node-2", 
            "type": "awsService",
            "position": {"x": 300, "y": 100},
            "data": {
                "service": {"id": "aws-api-gateway", "name": "Amazon API Gateway"},
                "config": {"requests": 1000},
                "label": "API Gateway",
                "cost": 0.335,
                "latency": 50
            }
        },
        {
            "id": "node-3",
            "type": "awsService", 
            "position": {"x": 500, "y": 100},
            "data": {
                "service": {"id": "aws-lambda", "name": "AWS Lambda"},
                "config": {"memory": 512},
                "label": "Lambda",
                "cost": 0.334,
                "latency": 100
            }
        },
        {
            "id": "node-4",
            "type": "awsService",
            "position": {"x": 700, "y": 100}, 
            "data": {
                "service": {"id": "aws-s3", "name": "Amazon S3"},
                "config": {"storage": 100},
                "label": "S3",
                "cost": 0.174,
                "latency": 25
            }
        },
        {
            "id": "node-5",
            "type": "awsService",
            "position": {"x": 500, "y": 300},
            "data": {
                "service": {"id": "aws-dynamodb", "name": "Amazon DynamoDB"},
                "config": {"workload": 1},
                "label": "DynamoDB", 
                "cost": 0.337,
                "latency": 10
            }
        }
    ],
    "edges": [
        {
            "id": "edge-1",
            "source": "node-1",
            "target": "node-2",
            "type": "smoothstep"
        },
        {
            "id": "edge-2", 
            "source": "node-2",
            "target": "node-3",
            "type": "smoothstep"
        },
        {
            "id": "edge-3",
            "source": "node-3", 
            "target": "node-4",
            "type": "smoothstep"
        },
        {
            "id": "edge-4",
            "source": "node-3",
            "target": "node-5", 
            "type": "smoothstep"
        }
    ],
    "name": "Test Architecture",
    "metadata": {"archType": "Microservices"}
}

def test_export_import_cycle():
    """Test the complete export/import cycle"""
    print("🧪 Testing Export/Import Cycle")
    print("=" * 50)
    
    print(f"📊 Input Data:")
    print(f"   Nodes: {len(sample_architecture_data['nodes'])}")
    print(f"   Edges: {len(sample_architecture_data['edges'])}")
    
    # Test the export process
    try:
        # Import the backend functions
        import sys
        sys.path.append('backend')
        from app.api.architecture import convert_react_node_to_pyqt5_shape, convert_react_edge_to_pyqt5_arrow
        
        print("\n🔄 Testing Export Process...")
        
        # Convert nodes to shapes
        shape_map = {}
        items = []
        
        for i, node in enumerate(sample_architecture_data['nodes']):
            shape = convert_react_node_to_pyqt5_shape(node)
            shape_map[node['id']] = shape
            
            # Store original React Flow node ID in the shape for later reference
            shape.original_react_id = node['id']
            shape.original_index = i
            shape.unique_id = f"shape_{i}_{node['id']}"
            
            item_data = {
                'object': shape,
                'pos': (shape.x(), shape.y()),
                'cost': shape.Cost,
                'latency': shape.Latency,
                'attributes': shape.attributes,
                'original_react_id': node['id'],
                'original_index': i,
                'unique_id': shape.unique_id
            }
            items.append(item_data)
            print(f"   ✅ Converted node {node['id']} to shape {shape.name}")
        
        # Convert edges to adjacency matrix
        adjacency_matrix = []
        edge_conversion_errors = []
        
        for i, edge in enumerate(sample_architecture_data['edges']):
            try:
                source_id = edge.get('source')
                target_id = edge.get('target')
                
                if source_id not in shape_map:
                    print(f"   ⚠️  Edge {i}: Source node '{source_id}' not found in shape_map")
                    continue
                    
                if target_id not in shape_map:
                    print(f"   ⚠️  Edge {i}: Target node '{target_id}' not found in shape_map")
                    continue
                
                # Convert edge to arrow
                arrow = convert_react_edge_to_pyqt5_arrow(edge, shape_map)
                
                # Create adjacency matrix entry
                adjacency_entry = (
                    arrow.start_item,
                    arrow.end_item,
                    (arrow.Cost, arrow.Latency, arrow.attributes)
                )
                adjacency_matrix.append(adjacency_entry)
                print(f"   ✅ Converted edge {edge.get('id')} to arrow {arrow.start_item.name} -> {arrow.end_item.name}")
                
            except Exception as e:
                error_msg = f"Error converting edge {edge.get('id', f'edge_{i}')}: {str(e)}"
                print(f"   ❌ {error_msg}")
                edge_conversion_errors.append(error_msg)
        
        print(f"\n📦 Export Results:")
        print(f"   Items: {len(items)}")
        print(f"   Adjacency Matrix: {len(adjacency_matrix)}")
        print(f"   Edge Conversion Errors: {len(edge_conversion_errors)}")
        
        # Create canvas data
        canvas_data = {
            'items': items,
            'adjacency_matrix': adjacency_matrix,
            'arch_type': sample_architecture_data['metadata'].get('archType', 'Microservices'),
            'mapping': {},
            'vpcsquares': {},
            'vpclist': []
        }
        
        # Test the import process
        print("\n🔄 Testing Import Process...")
        from app.api.architecture import convert_pickle_to_json
        
        converted_data = convert_pickle_to_json(canvas_data)
        
        print(f"\n📊 Import Results:")
        print(f"   Items: {len(converted_data.get('items', []))}")
        print(f"   Adjacency Matrix: {len(converted_data.get('adjacency_matrix', []))}")
        
        # Test frontend conversion
        print("\n🔄 Testing Frontend Conversion...")
        sys.path.append('frontend/src/utils')
        
        # Since we can't import TypeScript directly, let's simulate the conversion logic
        nodes = []
        edges = []
        
        # Convert items to nodes
        for i, item in enumerate(converted_data.get('items', [])):
            node_id = item['object'].get('original_react_id') or f'node-{i}'
            nodes.append({
                'id': node_id,
                'data': {
                    'label': item['object']['name'],
                    'originalName': item['object']['name']
                }
            })
        
        # Convert adjacency matrix to edges
        for i, connection in enumerate(converted_data.get('adjacency_matrix', [])):
            source_obj, target_obj, connection_data = connection
            
            # Find source and target nodes
            source_id = None
            target_id = None
            
            if source_obj.get('original_react_id'):
                source_id = source_obj['original_react_id']
            elif source_obj.get('id') is not None and source_obj['id'] < len(nodes):
                source_id = nodes[source_obj['id']]['id']
            
            if target_obj.get('original_react_id'):
                target_id = target_obj['original_react_id']
            elif target_obj.get('id') is not None and target_obj['id'] < len(nodes):
                target_id = nodes[target_obj['id']]['id']
            
            if source_id and target_id:
                edges.append({
                    'id': f'edge-{source_id}-{target_id}-{i}',
                    'source': source_id,
                    'target': target_id
                })
                print(f"   ✅ Created edge: {source_id} -> {target_id}")
            else:
                print(f"   ❌ Failed to create edge {i}: source_id={source_id}, target_id={target_id}")
        
        print(f"\n🎯 Final Results:")
        print(f"   Original Nodes: {len(sample_architecture_data['nodes'])}")
        print(f"   Original Edges: {len(sample_architecture_data['edges'])}")
        print(f"   Converted Nodes: {len(nodes)}")
        print(f"   Converted Edges: {len(edges)}")
        
        # Check if we preserved all connections
        success = len(edges) == len(sample_architecture_data['edges'])
        if success:
            print("   ✅ SUCCESS: All connections preserved!")
        else:
            print("   ❌ FAILURE: Some connections were lost!")
            
        return success
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_export_import_cycle()
    exit(0 if success else 1)
