#!/usr/bin/env python3
"""
Test script to debug the Load Architecture functionality.
This script simulates the backend processing of a pickle file to identify connection issues.
"""

import pickle
import sys
import os

def test_pickle_loading(pickle_path):
    """Test loading and analyzing a pickle file"""
    print(f"🔍 Testing pickle file: {pickle_path}")
    
    if not os.path.exists(pickle_path):
        print(f"❌ File not found: {pickle_path}")
        return
    
    try:
        # Load the pickle file
        with open(pickle_path, 'rb') as f:
            canvas_data = pickle.load(f)
        
        print(f"✅ Successfully loaded pickle file")
        print(f"📊 Data type: {type(canvas_data)}")
        
        if isinstance(canvas_data, dict):
            print(f"📋 Keys: {list(canvas_data.keys())}")
            
            # Analyze items
            items = canvas_data.get('items', [])
            print(f"🔢 Items count: {len(items)}")
            
            for i, item in enumerate(items[:3]):  # Show first 3 items
                print(f"  Item {i}:")
                print(f"    Type: {type(item)}")
                if isinstance(item, dict):
                    print(f"    Keys: {list(item.keys())}")
                    obj = item.get('object')
                    if obj:
                        print(f"    Object type: {type(obj)}")
                        print(f"    Object name: {getattr(obj, 'name', 'No name')}")
                        print(f"    Object ID: {id(obj)}")
                elif hasattr(item, 'name'):
                    print(f"    Name: {getattr(item, 'name', 'No name')}")
                    print(f"    Object ID: {id(item)}")
            
            # Analyze adjacency matrix
            adj_matrix = canvas_data.get('adjacency_matrix', [])
            print(f"🔗 Adjacency matrix count: {len(adj_matrix)}")
            
            for i, conn in enumerate(adj_matrix[:3]):  # Show first 3 connections
                print(f"  Connection {i}:")
                print(f"    Type: {type(conn)}")
                print(f"    Length: {len(conn) if hasattr(conn, '__len__') else 'N/A'}")
                
                if hasattr(conn, '__len__') and len(conn) >= 2:
                    source_obj = conn[0]
                    target_obj = conn[1]
                    
                    print(f"    Source: {type(source_obj)} - {getattr(source_obj, 'name', 'No name')} (ID: {id(source_obj)})")
                    print(f"    Target: {type(target_obj)} - {getattr(target_obj, 'name', 'No name')} (ID: {id(target_obj)})")
                    
                    if len(conn) > 2:
                        print(f"    Connection data: {conn[2]}")
        
        # Test object ID mapping
        print("\n🗺️  Testing object ID mapping:")
        object_id_map = {}
        
        if isinstance(canvas_data, dict) and 'items' in canvas_data:
            for i, item_data in enumerate(canvas_data['items']):
                original_object = item_data.get('object') if isinstance(item_data, dict) else item_data
                if original_object:
                    object_id_map[id(original_object)] = i
                    print(f"  Mapped object ID {id(original_object)} to index {i}")
        
        print(f"📊 Total objects mapped: {len(object_id_map)}")
        
        # Test connection mapping
        if isinstance(canvas_data, dict) and 'adjacency_matrix' in canvas_data:
            print("\n🔗 Testing connection mapping:")
            for i, connection in enumerate(canvas_data['adjacency_matrix'][:3]):
                if hasattr(connection, '__len__') and len(connection) >= 2:
                    source_obj = connection[0]
                    target_obj = connection[1]
                    
                    source_obj_id = id(source_obj)
                    target_obj_id = id(target_obj)
                    
                    source_mapped_id = object_id_map.get(source_obj_id)
                    target_mapped_id = object_id_map.get(target_obj_id)
                    
                    print(f"  Connection {i}:")
                    print(f"    Source ID {source_obj_id} -> Mapped ID {source_mapped_id}")
                    print(f"    Target ID {target_obj_id} -> Mapped ID {target_mapped_id}")
                    
                    if source_mapped_id is None:
                        print(f"    ❌ Source object not found in mapping!")
                    if target_mapped_id is None:
                        print(f"    ❌ Target object not found in mapping!")
        
    except Exception as e:
        print(f"❌ Error loading pickle file: {e}")
        import traceback
        traceback.print_exc()

def main():
    # Test with a few different pickle files
    test_files = [
        "MapleGUI/demo.pkl",
        "MapleGUI/demo1.pkl", 
        "MapleGUI/demo_arch.pkl",
        "MapleGUI/vigilantIQ.pkl",
        "MapleGUI/retailMS.pkl"
    ]
    
    for pickle_file in test_files:
        if os.path.exists(pickle_file):
            print(f"\n{'='*60}")
            test_pickle_loading(pickle_file)
            break  # Test only the first available file
    else:
        print("❌ No test pickle files found")

if __name__ == "__main__":
    main()
